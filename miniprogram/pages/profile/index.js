// 个人页面 - 用户信息和设置
import { api } from '../../core/api/index.js'
import { formatTime, formatDateTime, formatRelativeTime, formatRefreshTime } from '../../utils/time-utils.js'
const { isVipMember, showVipMembershipInfo, getMembershipTypeText } = require('../../utils/membership.js')

Page({
  data: {
    // 用户信息
    userInfo: {
      no: null,
      avatar: '',
      nickname: '加载中...',
      vip: {
        status: false,
        expiredAt: null
      },
      isLoggedIn: false
    },

    // 用户统计数据
    userStats: {
      registrationDays: 0,
      redemptionCodes: 0
    },

    // 编辑状态
    editingNickname: '',
    nicknameChanged: false,

    // 模态框相关
    showUserEditModal: false,
    tempUserInfo: {
      nickname: '',
      avatar: ''
    },
    focusNickname: false,

    // 会员信息
    membershipInfo: {
      expireDate: '',
      expireDateText: '',
      daysRemaining: 0,
      isNewUser: false,
      canWatchAd: true,
      lastAdWatchTime: null
    },

    // VIP记录相关
    showVipRecordsModal: false,
    vipRecords: [],
    vipRecordsStats: null,
    hasMoreVipRecords: false,
    vipRecordsPage: 0,
    vipRecordsPageSize: 10,

    // 同步状态
    syncStatus: {
      isInitialized: false,
      isSyncing: false,
      lastSyncTime: null,
      lastSyncTimeText: '从未同步',
      hasPermission: false
    },

    // 签到状态
    checkInStatus: {
      hasCheckedInToday: false,
      consecutiveDays: 0,
      totalDays: 0,
      nextReward: 1,
      loading: true
    },

    // 设置选项
    settings: {
      version: '0.2.0',
      developer: 'Panda',
      lastUpdateTime: '2025-07-15'
    },
    

    
    // 货币选项设置
    currencyOptions: [
      { value: 'CNY', label: '人民币 (¥)', symbol: '¥' },
      { value: 'USD', label: '美元 ($)', symbol: '$' },
      { value: 'EUR', label: '欧元 (€)', symbol: '€' },
      { value: 'JPY', label: '日元 (¥)', symbol: '¥' },
      { value: 'GBP', label: '英镑 (£)', symbol: '£' },
      { value: 'KRW', label: '韩元 (₩)', symbol: '₩' },
      { value: 'HKD', label: '港币 (HK$)', symbol: 'HK$' },
      { value: 'TWD', label: '新台币 (NT$)', symbol: 'NT$' }
    ],
    selectedCurrencyIndex: 0, // 默认选择人民币
    currentCurrency: 'CNY', // 当前选择的货币代码

    // 兑换码模态框
    showRedeemModal: false,
    defaultRedeemCode: '',

    // VIP到期时间显示文本
    vipExpireText: ''
  },

  /**
   * 页面加载时
   */
  onLoad() {
    console.log('个人页面加载开始')

    // 初始化服务
    const app = getApp()
    this.workHistoryService = app.getWorkHistoryService()
    this.timeSegmentService = app.getTimeSegmentService()
    this.dashboardService = app.getDashboardService()
    this.dataImportExportService = app.getDataImportExportService()

    // 获取全局管理器
    this.dataManager = getApp().getDataManager()
    this.userManager = getApp().getUserManager()
    this.syncManager = getApp().getSyncManager()

    // 初始化导入导出服务
    this.dataImportExportService.initialize(this.dataManager)

    // 确保用户信息已加载
    this.ensureUserInfo()

    // 加载会员信息
    this.loadMembershipInfo()

    // 加载同步状态
    this.loadSyncStatus()

    // 加载签到状态
    this.loadCheckInStatus()

    // 加载设置数据
    this.loadUserSettings()

    // 注册数据变化监听器
    this.registerDataChangeListener()
    this.registerUserChangeListener()
    this.registerSyncChangeListener()

    console.log('个人页面加载完成')
  },

  /**
   * 确保用户信息已加载
   */
  async ensureUserInfo() {
    try {
      // 确保应用级别的用户信息已加载
      await getApp().ensureUserInfo()

      // 加载页面级别的用户信息
      this.loadUserInfo()
    } catch (error) {
      console.error('确保用户信息失败:', error)
    }
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('[Profile] 个人页面显示')

    // 确保用户信息已加载
    this.ensureUserInfo()

    // 刷新数据
    this.loadUserInfo()
    this.loadUserSettings()
    this.loadCheckInStatus()
    this.loadUserStats()
    this.loadMembershipInfo()

    // 延迟加载同步状态，确保管理器已初始化
    setTimeout(() => {
      this.loadSyncStatus()
    }, 100)
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    console.log('设置页面卸载')
    
    // 移除数据变化监听器
    this.unregisterDataChangeListener()
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    try {
      const userInfo = this.userManager.getUserInfo()
      const isLoggedIn = this.userManager.isUserLoggedIn()

      this.setData({
        'userInfo.no': userInfo ? userInfo.no : null,
        'userInfo.avatar': userInfo ? userInfo.avatar : '',
        'userInfo.nickname': userInfo ? userInfo.nickname : '未登录',
        'userInfo.vip': userInfo ? userInfo.vip : { status: false, expiredAt: null },
        'userInfo.points': userInfo ? (userInfo.points || 0) : 0,
        'userInfo.isTestUser': userInfo ? (userInfo.isTestUser || false) : false,
        'userInfo.isLoggedIn': isLoggedIn
      })

      // 计算VIP到期时间显示文本
      this.updateVipExpireText()

      console.log('用户信息加载完成:', userInfo)
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  },

  /**
   * 注册数据变化监听器
   */
  registerDataChangeListener() {
    this.dataChangeListener = (userData) => {
      console.log('收到数据变化通知，刷新个人页面数据')
      this.loadUserSettings()
    }

    this.dataManager.addChangeListener(this.dataChangeListener)
  },

  /**
   * 加载同步状态
   */
  loadSyncStatus() {
    try {
      if (!this.syncManager) {
        console.warn('同步管理器未初始化')
        return
      }

      const syncStatus = this.syncManager.getSyncStatus()

      this.setData({
        'syncStatus.isInitialized': syncStatus.isInitialized,
        'syncStatus.isSyncing': syncStatus.isSyncing,
        'syncStatus.lastSyncTime': syncStatus.lastSyncTime,
        'syncStatus.lastSyncTimeText': syncStatus.lastSyncTimeText,
        'syncStatus.hasPermission': syncStatus.hasPermission
      })

      console.log('同步状态加载完成:', syncStatus)
    } catch (error) {
      console.error('加载同步状态失败:', error)

      // 设置默认状态
      this.setData({
        'syncStatus.isInitialized': false,
        'syncStatus.isSyncing': false,
        'syncStatus.lastSyncTime': null,
        'syncStatus.lastSyncTimeText': '未同步',
        'syncStatus.hasPermission': false
      })
    }
  },

  /**
   * 注册用户信息变化监听器
   */
  registerUserChangeListener() {
    this.userChangeListener = (userInfo) => {
      console.log('收到用户信息变化通知，更新页面显示')
      this.loadUserInfo()
    }

    this.userManager.addChangeListener(this.userChangeListener)
  },

  /**
   * 注册同步状态变化监听器
   */
  registerSyncChangeListener() {
    this.syncChangeListener = (type, data) => {
      console.log('收到同步状态变化通知:', type, data)
      this.loadSyncStatus()

      // 根据同步事件类型显示不同提示
      switch (type) {
        case 'sync_success':
          wx.showToast({
            title: '数据同步成功',
            icon: 'success'
          })
          break
        case 'sync_error':
          wx.showToast({
            title: '数据同步失败',
            icon: 'error'
          })
          break
        case 'data_applied':
          wx.showToast({
            title: '数据已更新',
            icon: 'success'
          })
          break
      }
    }

    this.syncManager.addChangeListener(this.syncChangeListener)
  },

  /**
   * 移除数据变化监听器
   */
  unregisterDataChangeListener() {
    if (this.dataChangeListener) {
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }

    if (this.userChangeListener) {
      this.userManager.removeChangeListener(this.userChangeListener)
      this.userChangeListener = null
    }

    if (this.syncChangeListener) {
      this.syncManager.removeChangeListener(this.syncChangeListener)
      this.syncChangeListener = null
    }
  },

  /**
   * 加载用户设置
   */
  loadUserSettings() {
    try {
      const settings = this.dataManager.getSettings()
      
      // 加载货币设置
      const incomeSettings = settings.income || {}
      const currencyCode = incomeSettings.currencyCode || 'CNY'
      const currencySymbol = incomeSettings.currencySymbol || '¥'

      // 查找货币选项索引
      const selectedCurrencyIndex = this.data.currencyOptions.findIndex(option => option.value === currencyCode)

      this.setData({
        currentCurrency: currencyCode,
        selectedCurrencyIndex: selectedCurrencyIndex !== -1 ? selectedCurrencyIndex : 0
      })
      
      console.log('用户设置加载完成')
    } catch (error) {
      console.error('加载用户设置失败:', error)
    }
  },

  /**
   * 货币选择变化
   */
  onCurrencyChange(e) {
    const selectedIndex = e.detail.value
    const selectedOption = this.data.currencyOptions[selectedIndex]
    
    try {
      // 更新到数据管理器
      const settings = this.dataManager.getSettings()
      settings.income.currencyCode = selectedOption.value
      settings.income.currencySymbol = selectedOption.symbol
      this.dataManager.updateSettings(settings)
      
      this.setData({
        selectedCurrencyIndex: selectedIndex,
        currentCurrency: selectedOption.value
      })
      
      wx.showToast({
        title: '设置已保存',
        icon: 'success',
        duration: 1000
      })
      
      console.log('货币设置已更新:', selectedOption.label)
    } catch (error) {
      console.error('保存货币设置失败:', error)
      wx.showToast({
        title: '设置保存失败',
        icon: 'error'
      })
    }
  },

  /**
   * 查看更新日志
   */
  onViewChangelog() {
    wx.navigateTo({
      url: '/pages/changelog/index'
    })
  },

  /**
   * 跳转到意见反馈页面
   */
  onGoToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    })
  },

  /**
   * 打开更新日志
   */
  onOpenChangelog() {
    wx.navigateTo({
      url: '/pages/changelog/index'
    })
  },

  /**
   * 选择头像（在模态框中使用）
   */
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    console.log('选择头像:', avatarUrl)
    console.log('当前用户信息:', this.data.userInfo)

    this.setData({
      'tempUserInfo.avatar': avatarUrl
    })

    console.log('临时用户信息更新后:', this.data.tempUserInfo)
  },



  /**
   * 刷新用户信息
   */
  async onRefreshUserInfo() {
    wx.showLoading({ title: '刷新中...' })

    try {
      const result = await this.userManager.refreshUserInfo()
      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      })
      console.error('刷新用户信息失败:', error)
    }
  },

  /**
   * 加载会员信息
   */
  async loadMembershipInfo() {
    try {
      const userInfo = this.data.userInfo

      if (userInfo.vip.status && userInfo.vip.expiredAt) {
        // 计算VIP到期信息
        const expireDate = new Date(userInfo.vip.expiredAt)
        const expireDateText = this.formatDate(expireDate)
        const daysRemaining = Math.ceil((expireDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

        this.setData({
          membershipInfo: {
            expireDate: expireDateText,
            expireDateText: expireDateText,
            daysRemaining: Math.max(0, daysRemaining),
            isNewUser: false,
            canWatchAd: this.checkCanWatchAd(),
            lastAdWatchTime: null
          }
        })
      } else {
        // 免费用户或VIP已过期
        this.setDefaultMembershipInfo(false)
      }
    } catch (error) {
      console.error('加载会员信息失败:', error)
      // 出错时使用默认值
      this.setDefaultMembershipInfo(this.data.userInfo.vip.status)
    }
  },

  /**
   * 设置默认会员信息
   */
  setDefaultMembershipInfo(isVip) {
    const isNewUser = this.checkIsNewUser()

    this.setData({
      membershipInfo: {
        expireDate: isVip ? '获取中...' : '',
        expireDateText: isVip ? '获取中...' : '',
        daysRemaining: 0,
        isNewUser: isNewUser,
        canWatchAd: this.checkCanWatchAd(),
        lastAdWatchTime: null
      }
    })
  },

  /**
   * 检查是否为新用户
   */
  checkIsNewUser() {
    // 简化逻辑：检查用户注册时间是否在7天内
    // 实际应该从用户管理器获取准确的注册时间和新用户状态
    try {
      const userInfo = this.data.userInfo
      // 模拟：如果没有userId或userId为空，认为是新用户
      return !userInfo.userId || userInfo.userId === '未知'
    } catch (error) {
      console.error('检查新用户状态失败:', error)
      return false
    }
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 查看会员权益
   */
  onViewMembershipBenefits() {
    showVipMembershipInfo()
  },

  /**
   * 会员卡片按钮点击
   */
  onMembershipAction() {
    console.log('会员卡片按钮点击')

    if (this.data.userInfo.vip.status) {
      // VIP用户 - 续期选项
      this.onExtendMembership()
    } else {
      // 免费用户 - 获取VIP选项
      this.onGetMembership()
    }
  },

  /**
   * 获取会员（免费用户）
   */
  onGetMembership() {
    console.log('获取会员')

    wx.showActionSheet({
      itemList: [
        '🎁 新用户福利（14天免费）',
        '🔑 输入激活码',
        '🎫 使用兑换码',
        '📺 观看广告获取会员',
        '📋 查看会员权益'
      ],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.onClaimNewUserBenefit()
            break
          case 1:
            this.onInputActivationCode()
            break
          case 2:
            this.onShowRedeemModal()
            break
          case 3:
            this.onWatchAdForMembership()
            break
          case 4:
            this.onViewMembershipBenefits()
            break
        }
      }
    })
  },

  /**
   * 续期会员（VIP用户）
   */
  onExtendMembership() {
    console.log('续期会员')

    const { membershipInfo } = this.data
    const canWatchAd = this.checkCanWatchAd()

    const itemList = [
      '🔑 输入激活码续期',
      '📋 查看会员权益',
      '📝 查看获取记录'
    ]

    if (canWatchAd) {
      itemList.splice(1, 0, '📺 观看广告续期')
    }

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.onInputActivationCode()
            break
          case 1:
            if (canWatchAd) {
              this.onWatchAdForMembership()
            } else {
              this.onViewMembershipBenefits()
            }
            break
          case 2:
            if (canWatchAd) {
              this.onViewMembershipBenefits()
            } else {
              this.onViewVipRecords()
            }
            break
          case 3:
            this.onViewVipRecords()
            break
        }
      }
    })
  },

  /**
   * 领取新用户福利
   */
  onClaimNewUserBenefit() {
    console.log('领取新用户福利')

    // 检查是否为新用户
    const userInfo = this.data.userInfo
    if (userInfo.vip.status) {
      wx.showToast({
        title: '您已经是VIP会员',
        icon: 'none'
      })
      return
    }

    // 模拟检查新用户状态
    wx.showModal({
      title: '新用户福利',
      content: '恭喜您！作为新用户，您可以免费获得14天VIP会员权益。\n\n功能开发中，敬请期待！',
      confirmText: '立即领取',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现新用户福利领取逻辑
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  /**
   * 输入激活码
   */
  onInputActivationCode() {
    console.log('输入激活码')

    wx.showModal({
      title: '激活码续期',
      content: '请联系客服获取激活码，输入激活码即可获得或续期VIP会员。\n\n功能开发中，敬请期待！',
      confirmText: '我知道了',
      showCancel: false
    })
  },

  /**
   * 观看广告获取会员
   */
  onWatchAdForMembership() {
    console.log('观看广告获取会员')

    if (!this.checkCanWatchAd()) {
      wx.showToast({
        title: '今日观看次数已用完',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '观看广告获取VIP',
      content: '观看完整的激励视频广告，即可获得VIP会员时长。\n\n每日限制观看次数。\n\n功能开发中，敬请期待！',
      confirmText: '开始观看',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现激励视频广告逻辑
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  /**
   * 检查是否可以观看广告
   */
  checkCanWatchAd() {
    const { membershipInfo } = this.data
    const now = new Date()
    const today = now.toDateString()

    // 检查今日是否已观看过广告（简化逻辑）
    if (membershipInfo.lastAdWatchTime) {
      const lastWatchDate = new Date(membershipInfo.lastAdWatchTime).toDateString()
      if (lastWatchDate === today) {
        return false // 今日已观看
      }
    }

    return true
  },

  /**
   * 查看VIP记录
   */
  async onViewVipRecords() {
    console.log('查看VIP记录')

    try {
      wx.showLoading({ title: '加载中...' })

      // 重置分页
      this.setData({
        vipRecordsPage: 0,
        vipRecords: []
      })

      // 加载VIP记录和统计
      await Promise.all([
        this.loadVipRecords(),
        this.loadVipRecordsStats()
      ])

      // 显示模态框
      this.setData({
        showVipRecordsModal: true
      })

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载VIP记录失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 关闭VIP记录模态框
   */
  onCloseVipRecordsModal() {
    this.setData({
      showVipRecordsModal: false
    })
  },

  /**
   * 加载VIP记录
   */
  async loadVipRecords() {
    try {
      const { vipRecordsPage, vipRecordsPageSize } = this.data

      console.log('[Profile] 开始加载VIP记录')

      const result = await api.user.getVipRecords({
        limit: vipRecordsPageSize,
        skip: vipRecordsPage * vipRecordsPageSize
      })

      if (result.success) {
        const records = result.data.map(record => ({
          ...record,
          createTimeText: formatDateTime(new Date(record.createTime))
        }))

        const currentRecords = vipRecordsPage === 0 ? [] : this.data.vipRecords

        this.setData({
          vipRecords: [...currentRecords, ...records],
          hasMoreVipRecords: records.length === vipRecordsPageSize
        })

        console.log(`[Profile] VIP记录加载成功: ${records.length}条记录`)
      } else {
        throw new Error(result.message || '获取VIP记录失败')
      }
    } catch (error) {
      console.error('[Profile] 加载VIP记录失败:', error)
      wx.showToast({
        title: error.message || '加载VIP记录失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载VIP记录统计
   */
  async loadVipRecordsStats() {
    try {
      console.log('[Profile] 开始加载VIP统计')

      const result = await api.user.getVipRecordsStats()

      if (result.success) {
        this.setData({
          vipRecordsStats: result.data
        })
        console.log('[Profile] VIP统计加载成功')
      } else {
        throw new Error(result.message || '获取VIP统计失败')
      }
    } catch (error) {
      console.error('[Profile] 加载VIP统计失败:', error)
      wx.showToast({
        title: error.message || '加载VIP统计失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载更多VIP记录
   */
  async onLoadMoreVipRecords() {
    if (!this.data.hasMoreVipRecords) {
      return
    }

    try {
      wx.showLoading({ title: '加载中...' })

      this.setData({
        vipRecordsPage: this.data.vipRecordsPage + 1
      })

      await this.loadVipRecords()

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载更多VIP记录失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  },



  /**
   * 加载签到状态
   */
  async loadCheckInStatus() {
    try {
      console.log('[Profile] 开始加载签到状态')

      const result = await api.checkIn.getCheckInStatus()

      if (result.success) {
        this.setData({
          checkInStatus: {
            ...result.data,
            loading: false
          }
        })
        console.log('[Profile] 签到状态加载成功')
      } else {
        console.error('[Profile] 获取签到状态失败:', result.message)
        this.setData({
          'checkInStatus.loading': false
        })
      }
    } catch (error) {
      console.error('[Profile] 加载签到状态失败:', error)
      this.setData({
        'checkInStatus.loading': false
      })
    }
  },

  /**
   * 快速签到
   */
  async onQuickCheckIn() {
    if (this.data.checkInStatus.loading) {
      return
    }

    if (this.data.checkInStatus.hasCheckedInToday) {
      wx.showToast({
        title: '今日已签到',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({
        'checkInStatus.loading': true
      })

      console.log('[Profile] 开始执行签到')

      const result = await api.checkIn.checkIn()

      if (result.success) {
        const data = result.data
        console.log('[Profile] 签到成功:', data)

        wx.showToast({
          title: `签到成功！+${data.reward}积分`,
          icon: 'success'
        })

        // 刷新签到状态
        this.loadCheckInStatus()

      } else {
        wx.showToast({
          title: result.message || '签到失败',
          icon: 'none'
        })
        this.setData({
          'checkInStatus.loading': false
        })
      }
    } catch (error) {
      console.error('[Profile] 签到失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
      this.setData({
        'checkInStatus.loading': false
      })
    }
  },

  /**
   * 跳转到签到页面
   */
  onGoToCheckIn() {
    wx.navigateTo({
      url: '/pages/check-in/index'
    })
  },

  /**
   * 查看积分详情
   */
  onViewPoints() {
    wx.navigateTo({
      url: '/pages/points/index'
    })
  },

  /**
   * 跳转到积分商店
   */
  onGoToStore() {
    wx.navigateTo({
      url: '/pages/store/index'
    })
  },



  /**
   * 升级会员
   */
  onUpgradeMembership() {
    wx.showModal({
      title: '获取VIP会员',
      content: 'VIP会员功能即将上线，敬请期待！\n\n获取后您将享受：\n• 无限工作履历和时间段\n• 高级数据分析功能\n• 数据同步和导出\n• 优先客服支持',
      confirmText: '了解更多',
      cancelText: '暂不获取',
      success: (res) => {
        if (res.confirm) {
          showVipMembershipInfo()
        }
      }
    })
  },

  /**
   * 手动同步数据
   */
  async onManualSync() {
    // 检查权限
    if (!this.data.syncStatus.hasPermission) {
      const { checkFeaturePermission } = require('../../utils/membership.js')
      if (!checkFeaturePermission('data_sync', '数据同步功能')) {
        return
      }
    }

    if (this.data.syncStatus.isSyncing) {
      wx.showToast({
        title: '同步进行中...',
        icon: 'loading'
      })
      return
    }

    wx.showLoading({ title: '同步中...' })

    try {
      await this.syncManager.manualSync()
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('手动同步失败:', error)
    }
  },

  /**
   * 跳转到友情应用页面
   */
  onGoToFriendApps() {
    wx.navigateTo({
      url: '/pages/friend-apps/index'
    })
  },

  /**
   * 关于我们
   */
  onAbout() {
    wx.showModal({
      title: '关于时间跟踪器',
      content: `版本：${this.data.settings.version}\n开发者：${this.data.settings.developer}\n\n这是一个专业的时间追踪和收入管理工具，帮助您更好地管理工作时间和收入。\n\n感谢您的使用！`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 打开同步调试页面
   */
  onOpenSyncDebug() {
    wx.navigateTo({
      url: '/pages/sync-debug/index'
    })
  },



  /**
   * 显示兑换码模态框
   */
  onShowRedeemModal() {
    this.setData({
      showRedeemModal: true,
      defaultRedeemCode: ''
    })
  },

  /**
   * 关闭兑换码模态框
   */
  onCloseRedeemModal() {
    this.setData({
      showRedeemModal: false,
      defaultRedeemCode: ''
    })
  },

  /**
   * 兑换成功回调
   */
  onRedeemSuccess(event) {
    const { code, data } = event.detail
    console.log('兑换成功:', code, data)

    // 刷新用户信息
    this.loadUserInfo()

    // 关闭模态框
    this.onCloseRedeemModal()
  },

  /**
   * 更新VIP到期时间显示文本
   */
  updateVipExpireText() {
    const userInfo = this.data.userInfo

    if (!userInfo.vip.status || !userInfo.vip.expiredAt) {
      this.setData({
        vipExpireText: ''
      })
      return
    }

    const expireDate = new Date(userInfo.vip.expiredAt)
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    // 格式化日期为 YYYY-MM-DD
    const year = expireDate.getFullYear()
    const month = String(expireDate.getMonth() + 1).padStart(2, '0')
    const day = String(expireDate.getDate()).padStart(2, '0')
    const dateStr = `有效期至：${year}-${month}-${day}`

    let expireText = ''

    if (diffDays <= 0) {
      expireText = `${dateStr}（已过期）`
    } else if (diffDays === 1) {
      expireText = `${dateStr}（明天）`
    } else {
      expireText = `${dateStr}（${diffDays}天后）`
    }

    this.setData({
      vipExpireText: expireText
    })
  },

  // ==================== 用户信息编辑模态框相关方法 ====================

  /**
   * 打开用户信息编辑模态框
   */
  onEditUserInfo() {
    console.log('打开用户信息编辑模态框')

    // 初始化临时用户信息
    this.setData({
      tempUserInfo: {
        nickname: this.data.userInfo.nickname,
        avatar: this.data.userInfo.avatar
      },
      showUserEditModal: true,
      focusNickname: false
    })

    // 延迟聚焦昵称输入框
    setTimeout(() => {
      this.setData({
        focusNickname: true
      })
    }, 300)
  },

  /**
   * 关闭用户信息编辑模态框
   */
  onCloseUserEditModal() {
    console.log('关闭用户信息编辑模态框')

    this.setData({
      showUserEditModal: false,
      focusNickname: false
    })
  },

  /**
   * 模态框内容点击（阻止冒泡）
   */
  onModalContentTap() {
    // 阻止事件冒泡，防止关闭模态框
  },

  /**
   * 临时昵称输入
   */
  onTempNicknameInput(e) {
    const nickname = e.detail.value
    this.setData({
      'tempUserInfo.nickname': nickname
    })
  },

  /**
   * 保存用户信息
   */
  async onSaveUserInfo() {
    const { tempUserInfo } = this.data

    if (!tempUserInfo.nickname.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    try {
      console.log('保存用户信息开始:', tempUserInfo)
      console.log('当前用户信息:', this.data.userInfo)

      wx.showLoading({ title: '保存中...' })

      // 如果头像有变化，先上传头像
      let finalAvatar = tempUserInfo.avatar
      const avatarChanged = tempUserInfo.avatar && tempUserInfo.avatar !== this.data.userInfo.avatar

      console.log('头像是否变化:', avatarChanged)
      console.log('临时头像路径:', tempUserInfo.avatar)
      console.log('当前头像路径:', this.data.userInfo.avatar)

      if (avatarChanged) {
        // 检查是否是本地文件路径（需要上传）
        const isLocalFile = tempUserInfo.avatar.startsWith('http://tmp/') ||
                           tempUserInfo.avatar.startsWith('wxfile://') ||
                           tempUserInfo.avatar.includes('tmp_')

        console.log('是否为本地文件:', isLocalFile)

        if (isLocalFile) {
          console.log('开始上传头像到云存储:', tempUserInfo.avatar)
          const cloudPath = `avatars/${this.data.userInfo.no || 'user'}_${Date.now()}.jpg`

          try {
            const uploadResult = await wx.cloud.uploadFile({
              cloudPath: cloudPath,
              filePath: tempUserInfo.avatar
            })

            console.log('头像上传结果:', uploadResult)

            if (uploadResult.fileID) {
              finalAvatar = uploadResult.fileID
              console.log('头像上传成功，新的fileID:', finalAvatar)
            } else {
              console.error('头像上传失败，没有返回fileID')
              throw new Error('头像上传失败')
            }
          } catch (uploadError) {
            console.error('头像上传失败:', uploadError)
            wx.hideLoading()
            wx.showToast({
              title: '头像上传失败',
              icon: 'none'
            })
            return
          }
        } else {
          console.log('头像已经是云存储路径，无需上传')
        }
      }

      // 保存到用户管理器
      const result = await this.userManager.updateUserInfo({
        nickname: tempUserInfo.nickname.trim(),
        avatar: finalAvatar
      })

      wx.hideLoading()

      if (result.success) {
        // 更新用户信息
        this.setData({
          'userInfo.nickname': tempUserInfo.nickname.trim(),
          'userInfo.avatar': finalAvatar
        })

        // 关闭模态框
        this.onCloseUserEditModal()

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      wx.hideLoading()
      console.error('保存用户信息失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载用户统计数据
   */
  async loadUserStats() {
    try {
      const userInfo = this.data.userInfo

      // 计算注册天数（注册当天算第1天）
      let registrationDays = 1
      if (userInfo.createTime) {
        const createDate = new Date(userInfo.createTime)
        const now = new Date()
        // 计算天数差，注册当天也算1天
        registrationDays = Math.floor((now.getTime() - createDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
      }

      this.setData({
        'userStats.registrationDays': registrationDays,
        'userStats.redemptionCodes': userInfo.redemptionCodesCount || 0
      })
    } catch (error) {
      console.error('加载用户统计数据失败:', error)
    }
  },

  /**
   * 显示注册天数信息
   */
  onShowRegistrationDays() {
    const days = this.data.userStats.registrationDays
    wx.showModal({
      title: '注册信息',
      content: `您已经使用小程序 ${days} 天了，感谢使用！`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 查看兑换码页面
   */
  onViewRedemptionCodes() {
    wx.navigateTo({
      url: '/pages/redemption-codes/index'
    })
  },

  /**
   * 跳转到数据统计页面
   */
  onGoToStatistics() {
    wx.switchTab({
      url: '/pages/statistics/index'
    })
  }
})