<!--统计页面结构-->
<view class="statistics-page">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-content">
      <view class="navbar-title">数据统计</view>
      <view class="navbar-actions">
        <view class="refresh-btn" bindtap="onRefresh">
          <text class="icon">🔄</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载统计数据...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{hasError}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-message">{{errorMessage}}</text>
    <button class="retry-btn" bindtap="onRefresh">重试</button>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 工作履历选择区域 -->
    <view class="work-selector-section" wx:if="{{workHistories.length > 1}}">
      <view class="selector-header">
        <view class="selector-title">📋 工作履历选择</view>
        <view class="selector-toggle" bindtap="toggleWorkSelector">
          <text class="selected-count">已选择 {{selectedWorkIds.length}} 个履历</text>
          <text class="toggle-icon">{{showWorkSelector ? '▲' : '▼'}}</text>
        </view>
      </view>

      <!-- 工作履历选择器 -->
      <view class="work-selector {{showWorkSelector ? 'show' : 'hide'}}">
        <view class="selector-actions">
          <view class="action-btn" bindtap="selectAllWorkHistories">全选</view>
          <view class="action-btn" bindtap="clearWorkSelection">仅当前</view>
        </view>

        <view class="work-list">
          <view class="work-item {{item.selected ? 'selected' : ''}}"
                wx:for="{{workHistories}}"
                wx:key="id"
                data-work-id="{{item.id}}"
                bindtap="onWorkHistoryToggle">
            <view class="work-checkbox">
              <text class="checkbox-icon">{{item.selected ? '✓' : '○'}}</text>
            </view>
            <view class="work-info">
              <view class="work-name">{{item.displayName}}</view>
              <view class="work-period">
                {{item.startDate}} - {{item.endDate || '至今'}}
                <text class="current-tag" wx:if="{{item.isCurrent}}">当前</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间范围统计区域 -->
    <view class="timerange-section">
      <view class="section-header">
        <view class="section-title">📊 时间范围统计</view>
        <view class="section-subtitle">选择时间范围查看详细数据</view>
      </view>

      <!-- 时间范围选择器 -->
      <view class="time-range-selector">
        <scroll-view class="range-options" scroll-x="true">
          <view class="range-option {{item.active ? 'active' : ''}}"
                wx:for="{{timeRangeOptions}}"
                wx:key="key"
                data-range="{{item.key}}"
                bindtap="onTimeRangeChange">
            {{item.text}}
          </view>
        </scroll-view>
      </view>

    <!-- 概览统计卡片 -->
    <view class="overview-section">
      <view class="section-title">概览统计</view>
      <view class="stat-cards">
        <view class="stat-card" data-type="workTime" bindtap="onStatCardTap">
          <view class="card-icon">⏰</view>
          <view class="card-content">
            <view class="card-value">{{statistics.overview.totalWorkTimeText}}</view>
            <view class="card-label">总工作时长</view>
          </view>
        </view>

        <view class="stat-card" data-type="income" bindtap="onStatCardTap">
          <view class="card-icon">💰</view>
          <view class="card-content">
            <view class="card-value">¥{{statistics.overview.totalIncomeText}}</view>
            <view class="card-label">总收入</view>
          </view>
        </view>

        <view class="stat-card" data-type="workDays" bindtap="onStatCardTap">
          <view class="card-icon">📅</view>
          <view class="card-content">
            <view class="card-value">{{statistics.overview.workDays}}</view>
            <view class="card-label">工作天数</view>
          </view>
        </view>

        <view class="stat-card" data-type="hourlyRate" bindtap="onStatCardTap">
          <view class="card-icon">💵</view>
          <view class="card-content">
            <view class="card-value">¥{{statistics.overview.averageHourlyRateText}}</view>
            <view class="card-label">平均时薪</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 收入分解统计 -->
    <view class="income-breakdown-section" wx:if="{{statistics.incomeBreakdown}}">
      <view class="section-title">收入分解</view>
      <view class="breakdown-cards">
        <view class="breakdown-card">
          <view class="card-icon">⏰</view>
          <view class="card-content">
            <view class="card-value">¥{{statistics.incomeBreakdown.baseIncomeText}}</view>
            <view class="card-label">基础收入</view>
            <view class="card-subtitle">时间段工作收入</view>
          </view>
        </view>

        <view class="breakdown-card positive" wx:if="{{statistics.incomeBreakdown.extraIncome > 0}}">
          <view class="card-icon">📈</view>
          <view class="card-content">
            <view class="card-value">+¥{{statistics.incomeBreakdown.extraIncomeText}}</view>
            <view class="card-label">额外收入</view>
            <view class="card-subtitle">提成、奖金、补贴等</view>
          </view>
        </view>

        <view class="breakdown-card negative" wx:if="{{statistics.incomeBreakdown.deductions > 0}}">
          <view class="card-icon">📉</view>
          <view class="card-content">
            <view class="card-value">-¥{{statistics.incomeBreakdown.deductionsText}}</view>
            <view class="card-label">扣款</view>
            <view class="card-subtitle">请假、迟到、违规等</view>
          </view>
        </view>

        <view class="breakdown-card summary" wx:if="{{statistics.incomeBreakdown.hasAdjustments}}">
          <view class="card-icon">💰</view>
          <view class="card-content">
            <view class="card-value {{statistics.incomeBreakdown.adjustmentBalance >= 0 ? 'positive' : 'negative'}}">
              {{statistics.incomeBreakdown.adjustmentBalance >= 0 ? '+' : ''}}¥{{statistics.incomeBreakdown.adjustmentBalanceText}}
            </view>
            <view class="card-label">调整结余</view>
            <view class="card-subtitle">额外收入 - 扣款</view>
          </view>
        </view>
      </view>

      <!-- 收入调整详细统计 -->
      <view class="adjustment-details" wx:if="{{statistics.incomeBreakdown.hasAdjustments}}">
        <!-- 额外收入按类型统计 -->
        <view class="detail-section" wx:if="{{statistics.incomeBreakdown.extraIncomeByType && Object.keys(statistics.incomeBreakdown.extraIncomeByType).length > 0}}">
          <view class="detail-title">额外收入明细</view>
          <view class="type-stats">
            <view class="type-item" wx:for="{{statistics.incomeBreakdown.extraIncomeTypesList}}" wx:key="type">
              <view class="type-info">
                <view class="type-name">{{item.typeName}}</view>
                <view class="type-count">{{item.count}}次</view>
              </view>
              <view class="type-amount positive">+¥{{item.amountText}}</view>
            </view>
          </view>
        </view>

        <!-- 扣款按类型统计 -->
        <view class="detail-section" wx:if="{{statistics.incomeBreakdown.deductionsByType && Object.keys(statistics.incomeBreakdown.deductionsByType).length > 0}}">
          <view class="detail-title">扣款明细</view>
          <view class="type-stats">
            <view class="type-item" wx:for="{{statistics.incomeBreakdown.deductionsTypesList}}" wx:key="type">
              <view class="type-info">
                <view class="type-name">{{item.typeName}}</view>
                <view class="type-count">{{item.count}}次</view>
              </view>
              <view class="type-amount negative">-¥{{item.amountText}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 摸鱼统计 -->
    <view class="analysis-section" wx:if="{{statistics.fishing.fishingCount > 0}}">
      <view class="section-title">摸鱼统计</view>
      <view class="fishing-stats">
        <view class="fishing-overview">
          <view class="fishing-item">
            <view class="fishing-value">{{statistics.fishing.totalFishingTimeText}}</view>
            <view class="fishing-label">总摸鱼时长</view>
          </view>
          <view class="fishing-item">
            <view class="fishing-value">{{statistics.fishing.fishingCount}}</view>
            <view class="fishing-label">摸鱼次数</view>
          </view>
          <view class="fishing-item">
            <view class="fishing-value">{{statistics.fishing.averageFishingDurationText}}</view>
            <view class="fishing-label">平均时长</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时薪统计 -->
    <view class="analysis-section" wx:if="{{statistics.hourlyRate.count > 0}}">
      <view class="section-title">时薪统计</view>
      <view class="hourly-rate-stats">
        <view class="rate-item">
          <view class="rate-label">平均时薪</view>
          <view class="rate-value">¥{{statistics.hourlyRate.averageText}}</view>
        </view>
        <view class="rate-item">
          <view class="rate-label">最高时薪</view>
          <view class="rate-value">¥{{statistics.hourlyRate.maxText}}</view>
        </view>
        <view class="rate-item">
          <view class="rate-label">最低时薪</view>
          <view class="rate-value">¥{{statistics.hourlyRate.minText}}</view>
        </view>
      </view>
    </view>

    <!-- 工作履历统计 -->
    <view class="analysis-section" wx:if="{{statistics.workHistory.totalWorks > 0}}">
      <view class="section-title">工作履历</view>
      <view class="work-history-stats">
        <view class="history-item">
          <view class="history-value">{{statistics.workHistory.totalWorks}}</view>
          <view class="history-label">总工作数</view>
        </view>
        <view class="history-item">
          <view class="history-value">{{statistics.workHistory.currentWorks}}</view>
          <view class="history-label">在职工作</view>
        </view>
        <view class="history-item">
          <view class="history-value">{{statistics.workHistory.averageWorkDays}}</view>
          <view class="history-label">平均在职天数</view>
        </view>
      </view>
    </view>



    <!-- 对比分析卡片 -->
    <view class="analysis-section" wx:if="{{statistics.comparisonAnalysis}}">
      <view class="section-title">📊 环比分析</view>
      <view class="comparison-container">
        <view class="comparison-item">
          <view class="comparison-label">工作时长</view>
          <view class="comparison-values">
            <view class="current-value">{{statistics.comparisonAnalysis.current.totalWorkHours}}h</view>
            <view class="change-indicator {{statistics.comparisonAnalysis.changes.workHoursDirection}}">
              {{statistics.comparisonAnalysis.changes.workHoursText}}
            </view>
          </view>
        </view>

        <view class="comparison-item">
          <view class="comparison-label">总收入</view>
          <view class="comparison-values">
            <view class="current-value">¥{{statistics.comparisonAnalysis.current.totalIncome}}</view>
            <view class="change-indicator {{statistics.comparisonAnalysis.changes.incomeDirection}}">
              {{statistics.comparisonAnalysis.changes.incomeText}}
            </view>
          </view>
        </view>

        <view class="comparison-item">
          <view class="comparison-label">平均时薪</view>
          <view class="comparison-values">
            <view class="current-value">¥{{statistics.comparisonAnalysis.current.averageHourlyRate}}</view>
            <view class="change-indicator {{statistics.comparisonAnalysis.changes.hourlyRateDirection}}">
              {{statistics.comparisonAnalysis.changes.hourlyRateText}}
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 饼图分析区域 -->
    <view class="analysis-section" wx:if="{{statistics.incomeSourceAnalysis.sources.length > 0 || statistics.timeDistributionPie.segments.length > 0}}">
      <view class="section-title">📊 分布分析</view>
      <view class="pie-charts-container">
        <!-- 收入来源饼图 -->
        <view class="pie-chart-section" wx:if="{{statistics.incomeSourceAnalysis.sources.length > 0}}">
          <view class="pie-chart-title">💰 收入来源</view>
          <view class="pie-chart-wrapper">
            <view class="pie-chart income-source-pie">
              <view class="pie-center">
                <view class="pie-total">¥{{statistics.incomeSourceAnalysis.totalIncomeText}}</view>
                <view class="pie-label">总收入</view>
              </view>
            </view>
          </view>
          <view class="pie-legend">
            <view class="legend-item" wx:for="{{statistics.incomeSourceAnalysis.sources}}" wx:key="name" wx:if="{{index < 5}}">
              <view class="legend-color" style="background: {{item.color}};"></view>
              <view class="legend-text">{{item.name}} ({{item.percentage}}%)</view>
            </view>
          </view>
        </view>

        <!-- 时间分布饼图 -->
        <view class="pie-chart-section" wx:if="{{statistics.timeDistributionPie.segments.length > 0}}">
          <view class="pie-chart-title">⏰ 时间分布</view>
          <view class="pie-chart-wrapper">
            <view class="pie-chart time-distribution-pie">
              <view class="pie-center">
                <view class="pie-total">{{statistics.timeDistributionPie.totalHoursText}}</view>
                <view class="pie-label">总时长</view>
              </view>
            </view>
          </view>
          <view class="pie-legend">
            <view class="legend-item" wx:for="{{statistics.timeDistributionPie.segments}}" wx:key="type">
              <view class="legend-color" style="background: {{item.color}};"></view>
              <view class="legend-text">{{item.label}} ({{item.percentage}}%)</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 排行榜卡片 -->
    <view class="analysis-section" wx:if="{{statistics.rankingAnalysis}}">
      <view class="section-title">🏆 最佳表现</view>
      <view class="ranking-container">
        <!-- 最高收入 -->
        <view class="ranking-category" wx:if="{{statistics.rankingAnalysis.topIncome.length > 0}}">
          <view class="category-title">💰 最高收入日</view>
          <view class="ranking-list">
            <view class="ranking-item" wx:for="{{statistics.rankingAnalysis.topIncome}}" wx:key="date" wx:if="{{index < 3}}">
              <view class="rank-number">{{item.rank}}</view>
              <view class="rank-content">
                <view class="rank-date">{{item.dateText}}</view>
                <view class="rank-value">¥{{item.incomeText}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 最长工作时间 -->
        <view class="ranking-category" wx:if="{{statistics.rankingAnalysis.topWorkHours.length > 0}}">
          <view class="category-title">⏰ 最长工作日</view>
          <view class="ranking-list">
            <view class="ranking-item" wx:for="{{statistics.rankingAnalysis.topWorkHours}}" wx:key="date" wx:if="{{index < 3}}">
              <view class="rank-number">{{item.rank}}</view>
              <view class="rank-content">
                <view class="rank-date">{{item.dateText}}</view>
                <view class="rank-value">{{item.workHoursText}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 收入增长趋势 -->
    <view class="analysis-section" wx:if="{{statistics.incomeGrowthTrend.trendData.length > 0}}">
      <view class="section-title">📈 收入增长趋势</view>
      <view class="growth-container">
        <view class="growth-summary">
          <view class="summary-item">
            <view class="summary-value">{{statistics.incomeGrowthTrend.totalMonths}}</view>
            <view class="summary-label">统计月数</view>
          </view>
          <view class="summary-item">
            <view class="summary-value">¥{{statistics.incomeGrowthTrend.avgMonthlyIncome}}</view>
            <view class="summary-label">月均收入</view>
          </view>
        </view>

        <view class="growth-chart">
          <view class="growth-item" wx:for="{{statistics.incomeGrowthTrend.trendData}}" wx:key="month">
            <view class="growth-month">{{item.monthName}}</view>
            <view class="growth-bar-container">
              <view class="growth-bar" style="height: {{(item.income / statistics.incomeGrowthTrend.avgMonthlyIncome) * 50}}px; background: linear-gradient(180deg, #10b981 0%, #059669 100%);"></view>
            </view>
            <view class="growth-value">¥{{item.income}}</view>
            <view class="growth-change {{item.growthDirection}}" wx:if="{{item.growth !== 0}}">
              {{item.growth > 0 ? '+' : ''}}{{item.growth}}%
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历热力图卡片 -->
    <view class="analysis-section" wx:if="{{statistics.calendarHeatmap.calendarData}}">
      <view class="heatmap-header">
        <view class="section-title">📅 数据热力图</view>
        <view class="heatmap-type-selector">
          <view
            class="type-btn {{item.active ? 'active' : ''}} {{item.color}}"
            wx:for="{{heatmapTypes}}"
            wx:key="key"
            data-type="{{item.key}}"
            bindtap="onHeatmapTypeChange">
            {{item.text}}
          </view>
        </view>
      </view>

      <view class="heatmap-container">
        <!-- 日历信息 -->
        <view class="calendar-info">
          <text class="calendar-period">{{statistics.calendarHeatmap.dateRangeText}}</text>
          <text class="calendar-stats">{{statistics.calendarHeatmap.workDays}} 个工作日</text>
        </view>

        <!-- 日历热力图网格 -->
        <view class="calendar-heatmap">
          <!-- 星期标题 -->
          <view class="calendar-weekdays">
            <view class="weekday-label">日</view>
            <view class="weekday-label">一</view>
            <view class="weekday-label">二</view>
            <view class="weekday-label">三</view>
            <view class="weekday-label">四</view>
            <view class="weekday-label">五</view>
            <view class="weekday-label">六</view>
          </view>

          <!-- 日历网格 -->
          <view class="calendar-grid">
            <view class="calendar-week" wx:for="{{statistics.calendarHeatmap.weeks}}" wx:key="index">
              <view
                class="calendar-day {{day.colorClass}} {{!day.isInRange ? 'out-of-range' : ''}}"
                wx:for="{{item}}"
                wx:for-item="day"
                wx:key="dateKey"
                data-date="{{day.dateKey}}"
                bindtap="onCalendarDayTap">
                <!-- 月份标签 -->
                <view class="month-label" wx:if="{{day.day === 1 && day.isInRange}}">
                  {{day.month}}月
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 热力图图例 -->
        <view class="heatmap-legend">
          <text class="legend-label">强度：</text>
          <view class="legend-colors">
            <view class="legend-item">
              <view class="legend-color heat-none"></view>
              <text class="legend-text">无</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-very-low" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-very-low" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-very-low" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-very-low" wx:else></view>
              <text class="legend-text">低</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-medium" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-medium" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-medium" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-medium" wx:else></view>
              <text class="legend-text">中</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-high" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-high" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-high" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-high" wx:else></view>
              <text class="legend-text">高</text>
            </view>
            <view class="legend-item">
              <view class="legend-color heat-green-very-high" wx:if="{{currentHeatmapType === 'income' || currentHeatmapType === 'hourlyRate'}}"></view>
              <view class="legend-color heat-red-very-high" wx:elif="{{currentHeatmapType === 'workTime'}}"></view>
              <view class="legend-color heat-gold-very-high" wx:elif="{{currentHeatmapType === 'fishTime'}}"></view>
              <view class="legend-color heat-blue-very-high" wx:else></view>
              <text class="legend-text">很高</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    </view>

    <!-- 历史总览区域 -->
    <view class="historical-section">
      <view class="section-header">
        <view class="section-title">📈 历史总览</view>
        <view class="section-subtitle">全时段数据统计</view>
      </view>

      <view class="historical-content">

      <!-- 总体数据 -->
      <view class="historical-overview" wx:if="{{statistics.historical.allTime}}">
        <view class="overview-stats">
          <view class="overview-item">
            <view class="overview-icon">⏰</view>
            <view class="overview-content">
              <view class="overview-value">{{statistics.historical.allTime.totalWorkTimeText}}</view>
              <view class="overview-label">总工作时长</view>
            </view>
          </view>

          <view class="overview-item">
            <view class="overview-icon">💰</view>
            <view class="overview-content">
              <view class="overview-value">¥{{statistics.historical.allTime.totalIncomeText}}</view>
              <view class="overview-label">总收入</view>
            </view>
          </view>

          <view class="overview-item">
            <view class="overview-icon">📅</view>
            <view class="overview-content">
              <view class="overview-value">{{statistics.historical.allTime.workDays}}</view>
              <view class="overview-label">工作天数</view>
            </view>
          </view>

          <view class="overview-item">
            <view class="overview-icon">⚡</view>
            <view class="overview-content">
              <view class="overview-value">¥{{statistics.historical.allTime.averageHourlyRateText}}</view>
              <view class="overview-label">平均时薪</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 历史最佳记录 -->
      <view class="best-records" wx:if="{{statistics.historical.bestRecords}}">
        <view class="records-title">🏆 历史最佳</view>
        <view class="records-grid">
          <view class="record-item" wx:if="{{statistics.historical.bestRecords.highestIncome.amount > 0}}">
            <view class="record-icon">💰</view>
            <view class="record-content">
              <view class="record-value">¥{{statistics.historical.bestRecords.highestIncome.amountText}}</view>
              <view class="record-label">最高单日收入</view>
              <view class="record-date">{{statistics.historical.bestRecords.highestIncome.dateText}}</view>
            </view>
          </view>

          <view class="record-item" wx:if="{{statistics.historical.bestRecords.longestWork.minutes > 0}}">
            <view class="record-icon">⏰</view>
            <view class="record-content">
              <view class="record-value">{{statistics.historical.bestRecords.longestWork.timeText}}</view>
              <view class="record-label">最长工作时间</view>
              <view class="record-date">{{statistics.historical.bestRecords.longestWork.dateText}}</view>
            </view>
          </view>

          <view class="record-item" wx:if="{{statistics.historical.bestRecords.bestEfficiency.rate > 0}}">
            <view class="record-icon">⚡</view>
            <view class="record-content">
              <view class="record-value">¥{{statistics.historical.bestRecords.bestEfficiency.rateText}}</view>
              <view class="record-label">最高时薪</view>
              <view class="record-date">{{statistics.historical.bestRecords.bestEfficiency.dateText}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 里程碑 -->
      <view class="milestones" wx:if="{{statistics.historical.milestones}}">
        <view class="milestones-title">🎯 里程碑</view>
        <view class="milestones-grid">
          <view class="milestone-item">
            <view class="milestone-number">{{statistics.historical.milestones.totalDays}}</view>
            <view class="milestone-label">总工作天数</view>
          </view>

          <view class="milestone-item">
            <view class="milestone-number">{{statistics.historical.milestones.totalMonths}}</view>
            <view class="milestone-label">工作月数</view>
          </view>

          <view class="milestone-item">
            <view class="milestone-number">{{statistics.historical.milestones.avgDaysPerMonth}}</view>
            <view class="milestone-label">月均工作天数</view>
          </view>

          <view class="milestone-item" wx:if="{{statistics.historical.milestones.firstWorkDateText}}">
            <view class="milestone-date">{{statistics.historical.milestones.firstWorkDateText}}</view>
            <view class="milestone-label">首次工作日期</view>
          </view>
        </view>
      </view>
      </view>
    </view>



    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </view>
</view>
