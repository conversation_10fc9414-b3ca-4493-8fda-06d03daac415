page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 统计页面样式 - 与日历页面统一风格 */
.statistics-page {
  min-height: 100vh;
  background-color: #F5F7FB;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  color: #333;
}

/* 自定义导航栏 */
.custom-navbar {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 24rpx 24rpx 0;
  border-radius: 20rpx;
  position: sticky;
  top: 24rpx;
  z-index: 100;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx 32rpx;
}

.navbar-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.refresh-btn {
  padding: 16rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.refresh-btn:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.05);
}

.refresh-btn .icon {
  font-size: 32rpx;
  color: #2d3748;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  margin: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(168, 237, 234, 0.3);
  border-top: 6rpx solid #a8edea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 32rpx;
  color: #4a5568;
  font-size: 28rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  margin: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.error-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  color: #e53e3e;
}

.error-message {
  color: #4a5568;
  font-size: 32rpx;
  margin-bottom: 48rpx;
  text-align: center;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.retry-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;
  padding: 20rpx 48rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.retry-btn:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.05);
}

/* 主要内容 */
.main-content {
  padding: 0 0 40rpx 0;
}

/* 工作履历选择区域 */
.work-selector-section {
  margin: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.selector-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.selector-toggle {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 12rpx 24rpx;
  background: rgba(168, 237, 234, 0.2);
  border-radius: 32rpx;
  transition: all 0.3s ease;
}

.selector-toggle:active {
  transform: scale(0.95);
  background: rgba(168, 237, 234, 0.3);
}

.selected-count {
  font-size: 24rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.toggle-icon {
  font-size: 24rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.work-selector {
  transition: all 0.3s ease;
  overflow: hidden;
}

.work-selector.show {
  max-height: 400px;
  opacity: 1;
}

.work-selector.hide {
  max-height: 0;
  opacity: 0;
}

.selector-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.action-btn {
  padding: 12rpx 24rpx;
  background: rgba(168, 237, 234, 0.8);
  color: #2d3748;
  border-radius: 32rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(168, 237, 234, 1);
}

.work-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.work-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.work-item:last-child {
  border-bottom: none;
}

.work-item.selected {
  background: rgba(168, 237, 234, 0.2);
}

.work-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.work-checkbox {
  margin-right: 24rpx;
}

.checkbox-icon {
  font-size: 32rpx;
  color: #a8edea;
}

.work-info {
  flex: 1;
}

.work-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.work-period {
  font-size: 24rpx;
  color: #718096;
  display: flex;
  align-items: center;
  gap: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.current-tag {
  padding: 4rpx 12rpx;
  background: rgba(16, 185, 129, 0.8);
  color: white;
  border-radius: 16rpx;
  font-size: 20rpx;
  text-shadow: none;
}

/* 时间范围选择器 */
.time-range-selector {
  padding: 32rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  margin: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.range-options {
  display: flex;
  white-space: nowrap;
  gap: 16rpx;
}

.range-option {
  display: inline-block;
  padding: 16rpx 32rpx;
  background: rgba(168, 237, 234, 0.2);
  color: #4a5568;
  border-radius: 40rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.range-option.active {
  background: rgba(168, 237, 234, 0.8);
  color: #2d3748;
  font-weight: 600;
}

.range-option:active {
  transform: scale(0.95);
}

/* 章节标题 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 32rpx;
  padding: 0 40rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 概览统计卡片 */
.overview-section {
  padding: 48rpx 0;
}

.stat-cards {
  display: flex;
  flex-wrap: wrap;
  padding: 0 24rpx;
  gap: 24rpx;
}

.stat-card {
  flex: 1;
  min-width: calc(50% - 12rpx);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.stat-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(168, 237, 234, 0.2);
  border-radius: 24rpx;
  color: #2d3748;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.card-label {
  font-size: 24rpx;
  color: #718096;
  margin-top: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 分析章节 */
.analysis-section {
  margin-top: 48rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  margin: 48rpx 24rpx 0 24rpx;
  border-radius: 20rpx;
  padding: 48rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.analysis-section .section-title {
  color: #2d3748;
  margin-bottom: 40rpx;
  padding: 0;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 饼图容器 */
.pie-chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}

.pie-chart-wrapper {
  position: relative;
  width: 300rpx;
  height: 300rpx;
}

.pie-chart {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  background: conic-gradient(
    #22c55e 0deg 120deg,
    #f97316 120deg 240deg,
    #9ca3af 240deg 360deg
  );
}

.time-pie {
  background: conic-gradient(
    #22c55e 0deg var(--work-angle, 120deg),
    #f97316 var(--work-angle, 120deg) var(--overtime-angle, 240deg),
    #9ca3af var(--overtime-angle, 240deg) 360deg
  );
}

.income-pie {
  background: conic-gradient(
    #3b82f6 0deg var(--work-income-angle, 180deg),
    #f59e0b var(--work-income-angle, 180deg) 360deg
  );
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.pie-total {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.pie-label {
  font-size: 24rpx;
  color: #718096;
  margin-top: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 图例 */
.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.work-color {
  background: #22c55e;
}

.overtime-color {
  background: #f97316;
}

.rest-color {
  background: #9ca3af;
}

.income-work-color {
  background: #3b82f6;
}

.income-overtime-color {
  background: #f59e0b;
}

.legend-text {
  font-size: 28rpx;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 时间分布 */
.distribution-item {
  margin-bottom: 40rpx;
}

.distribution-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.item-icon.work {
  background: rgba(34, 197, 94, 0.2);
  color: #059669;
}

.item-icon.overtime {
  background: rgba(249, 115, 22, 0.2);
  color: #ea580c;
}

.item-icon.rest {
  background: rgba(156, 163, 175, 0.2);
  color: #6b7280;
}

.item-info {
  flex: 1;
}

.item-label {
  font-size: 28rpx;
  color: #718096;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.item-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.item-percentage {
  font-size: 28rpx;
  font-weight: 600;
  color: #a8edea;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.progress-bar {
  height: 12rpx;
  background: rgba(168, 237, 234, 0.2);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

.progress-fill.work {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.progress-fill.overtime {
  background: linear-gradient(90deg, #f97316, #ea580c);
}

.progress-fill.rest {
  background: linear-gradient(90deg, #9ca3af, #6b7280);
}

/* 收入分析 */
.income-overview {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 40rpx;
  background: linear-gradient(135deg, rgba(168, 237, 234, 0.8), rgba(254, 214, 227, 0.8));
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  color: #2d3748;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.total-label {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.total-value {
  font-size: 56rpx;
  font-weight: 700;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-header {
  flex: 1;
}

.breakdown-label {
  font-size: 28rpx;
  color: #718096;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.breakdown-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.breakdown-percentage {
  font-size: 28rpx;
  font-weight: 600;
  color: #a8edea;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 摸鱼统计 */
.fishing-overview {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.fishing-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.fishing-label {
  font-size: 24rpx;
  color: #718096;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 时薪统计 */
.hourly-rate-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.rate-item {
  flex: 1;
}

.rate-label {
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.rate-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 工作履历统计 */
.work-history-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.history-item {
  flex: 1;
}

.history-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.history-label {
  font-size: 24rpx;
  color: #718096;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 热力图卡片头部 */
.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.heatmap-type-selector {
  display: flex;
  background: #f3f4f6;
  border-radius: 20px;
  padding: 2px;
  gap: 2px;
}

.type-btn {
  padding: 6px 12px;
  border-radius: 18px;
  font-size: 12px;
  color: #666;
  transition: all 0.3s ease;
  cursor: pointer;
}

.type-btn:active {
  transform: scale(0.95);
}

.type-btn.active.green {
  background: #10b981;
  color: white;
}

.type-btn.active.red {
  background: #ef4444;
  color: white;
}

.type-btn.active.gold {
  background: #f59e0b;
  color: white;
}

.type-btn.active.blue {
  background: #3b82f6;
  color: white;
}

/* 热力图容器 */
.heatmap-container {
  margin-top: 8px;
}

/* 日历信息 */
.calendar-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.calendar-period {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.calendar-stats {
  font-size: 12px;
  color: #666;
}

/* 星期标题 */
.calendar-weekdays {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-right: 8px;
  min-width: 20px;
}

.weekday-label {
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

/* 日历热力图容器 */
.calendar-heatmap {
  margin-bottom: 16px;
  display: flex;
  overflow-x: auto;
  padding-bottom: 8px;
}

.calendar-heatmap::-webkit-scrollbar {
  height: 4px;
}

.calendar-heatmap::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.calendar-heatmap::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.calendar-heatmap::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 日历网格 */
.calendar-grid {
  display: flex;
  gap: 2px;
  min-width: max-content;
}

.calendar-week {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.calendar-day {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
}

.calendar-day:active {
  transform: scale(1.1);
}

.calendar-day.out-of-range {
  opacity: 0.3;
}

/* 月份标签 */
.month-label {
  position: absolute;
  top: -18px;
  left: 0;
  font-size: 10px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

/* 热力图颜色 - 无数据 */
.heat-none {
  background: #ebedf0;
}

/* 绿色主题 (收入、时薪) - 更浅的颜色 */
.heat-green-very-low {
  background: #d4edda;
}

.heat-green-low {
  background: #c3e6cb;
}

.heat-green-medium {
  background: #a3d977;
}

.heat-green-high {
  background: #7bc96f;
}

.heat-green-very-high {
  background: #28a745;
}

/* 红色主题 (工作时间) - 更浅的颜色 */
.heat-red-very-low {
  background: #f8d7da;
}

.heat-red-low {
  background: #f5c6cb;
}

.heat-red-medium {
  background: #f1b0b7;
}

.heat-red-high {
  background: #ec7063;
}

.heat-red-very-high {
  background: #dc3545;
}

/* 金色主题 (摸鱼时间) - 更浅的颜色 */
.heat-gold-very-low {
  background: #fff3cd;
}

.heat-gold-low {
  background: #ffeaa7;
}

.heat-gold-medium {
  background: #fdcb6e;
}

.heat-gold-high {
  background: #f39c12;
}

.heat-gold-very-high {
  background: #e67e22;
}

/* 蓝色主题 (默认) - 更浅的颜色 */
.heat-blue-very-low {
  background: #d1ecf1;
}

.heat-blue-low {
  background: #bee5eb;
}

.heat-blue-medium {
  background: #85c1e9;
}

.heat-blue-high {
  background: #5dade2;
}

.heat-blue-very-high {
  background: #3498db;
}

/* 热力图图例 */
.heatmap-legend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 16px;
}

.legend-label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.legend-colors {
  display: flex;
  gap: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.legend-text {
  font-size: 10px;
  color: #666;
}

/* 趋势分析卡片 */
.trend-container {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.trend-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  gap: 12px;
}

.trend-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
}

.trend-content {
  flex: 1;
}

.trend-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.trend-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.trend-change {
  font-size: 12px;
  font-weight: 500;
}

.trend-change.up {
  color: #10b981;
}

.trend-change.down {
  color: #ef4444;
}

.trend-change.stable {
  color: #6b7280;
}

/* 对比分析卡片 */
.comparison-container {
  margin-top: 16px;
}

.comparison-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
}

.comparison-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.comparison-values {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.change-indicator {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.change-indicator.up {
  color: #10b981;
  background: #d1fae5;
}

.change-indicator.down {
  color: #ef4444;
  background: #fee2e2;
}

.change-indicator.stable {
  color: #6b7280;
  background: #f3f4f6;
}

/* 排行榜卡片 */
.ranking-container {
  margin-top: 16px;
}

.ranking-category {
  margin-bottom: 20px;
}

.category-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-left: 8px;
}

.ranking-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 8px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 4px;
  background: white;
  border-radius: 6px;
  gap: 12px;
}

.ranking-item:last-child {
  margin-bottom: 0;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #4f46e5;
  color: white;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rank-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rank-date {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.rank-value {
  font-size: 14px;
  color: #666;
  font-weight: 600;
}

/* 区域头部样式 */
.section-header {
  margin-bottom: 20px;
  text-align: center;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.section-subtitle {
  font-size: 12px;
  color: #666;
}

/* 实时概览区域 */
.realtime-section {
  margin-bottom: 32px;
  padding: 20px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.realtime-section .section-title,
.realtime-section .section-subtitle {
  color: white;
}

.realtime-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.realtime-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.card-icon {
  font-size: 20px;
}

.card-title {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
}

.card-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.card-badge.working {
  background: #10b981;
  color: white;
}

.card-badge.rest {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

.card-badge.active {
  background: #f59e0b;
  color: white;
}

.card-badge.inactive {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

.card-info {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.progress-item {
  margin-top: 8px;
}

.progress-label {
  font-size: 12px;
  margin-bottom: 6px;
  opacity: 0.9;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #10b981;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.streak-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.streak-item {
  text-align: center;
}

.streak-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.streak-label {
  font-size: 12px;
  opacity: 0.8;
}

.streak-divider {
  width: 1px;
  height: 30px;
  background: rgba(255, 255, 255, 0.3);
}

/* 时间范围统计区域 */
.timerange-section {
  margin-bottom: 32px;
}

/* 图表容器 */
.chart-container {
  margin-top: 16px;
}

.chart-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 16px;
  text-align: center;
}

/* 效率分布图表 */
.efficiency-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.chart-item {
  margin-bottom: 12px;
}

.chart-item:last-child {
  margin-bottom: 0;
}

.chart-label {
  font-size: 12px;
  color: #333;
  margin-bottom: 6px;
  font-weight: 500;
}

.chart-bar-container {
  position: relative;
  height: 24px;
  background: #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.chart-bar {
  height: 100%;
  border-radius: 12px;
  transition: width 0.3s ease;
}

.chart-value {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #333;
  font-weight: 500;
}

/* 收入来源图表 */
.income-source-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.source-item {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
}

.source-item:last-child {
  margin-bottom: 0;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.source-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.source-percentage {
  font-size: 12px;
  font-weight: 600;
  color: #3b82f6;
}

.source-bar-container {
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.source-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.source-details {
  display: flex;
  gap: 16px;
}

.source-detail {
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-label {
  font-size: 11px;
  color: #666;
}

.detail-value {
  font-size: 11px;
  font-weight: 600;
  color: #333;
}

/* 工作时长分布图表 */
.duration-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.duration-item {
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
}

.duration-item:last-child {
  margin-bottom: 0;
}

.duration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.duration-label {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.duration-count {
  font-size: 12px;
  font-weight: 600;
  color: #f59e0b;
}

.duration-bar-container {
  position: relative;
  height: 20px;
  background: #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 6px;
}

.duration-bar {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.duration-percentage {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #333;
  font-weight: 500;
}

.duration-stats {
  text-align: center;
}

.duration-stat {
  font-size: 11px;
  color: #666;
}

/* 饼图样式 */
.pie-charts-container {
  display: flex;
  flex-direction: column;
  gap: 48rpx;
  margin-top: 32rpx;
}

.pie-chart-section {
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.pie-chart-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 32rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.pie-chart-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.pie-chart {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: #f0f0f0;
  overflow: hidden;
}

/* 特定饼图的默认样式 */
.time-distribution-pie {
  background: conic-gradient(
    #22c55e 0deg 120deg,
    #f97316 120deg 240deg,
    #9ca3af 240deg 360deg
  );
}

.income-source-pie {
  background: conic-gradient(
    #3b82f6 0deg 180deg,
    #f59e0b 180deg 360deg
  );
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.pie-total {
  font-size: 24rpx;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.pie-label {
  font-size: 20rpx;
  color: #718096;
  margin-top: 4rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-text {
  font-size: 10px;
  color: #666;
}

/* 工作节奏分析 */
.rhythm-container {
  margin-top: 16px;
}

.rhythm-description {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-bottom: 16px;
}

.rhythm-chart {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.rhythm-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.rhythm-item:last-child {
  margin-bottom: 0;
}

.rhythm-icon {
  font-size: 20px;
  width: 32px;
  text-align: center;
}

.rhythm-content {
  flex: 1;
}

.rhythm-label {
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
}

.rhythm-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.rhythm-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.rhythm-fill.morning {
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

.rhythm-fill.afternoon {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
}

.rhythm-fill.night {
  background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 100%);
}

.rhythm-fill.allday {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.rhythm-percentage {
  font-size: 10px;
  color: #666;
  text-align: right;
}

/* 收入增长趋势 */
.growth-container {
  margin-top: 16px;
}

.growth-summary {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 12px;
  color: #666;
}

.growth-chart {
  display: flex;
  align-items: end;
  gap: 8px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow-x: auto;
}

.growth-item {
  flex: 1;
  min-width: 60px;
  text-align: center;
}

.growth-month {
  font-size: 10px;
  color: #666;
  margin-bottom: 8px;
  transform: rotate(-45deg);
  white-space: nowrap;
}

.growth-bar-container {
  display: flex;
  align-items: end;
  justify-content: center;
  height: 60px;
  margin-bottom: 8px;
}

.growth-bar {
  width: 20px;
  min-height: 4px;
  border-radius: 2px;
  transition: height 0.3s ease;
}

.growth-value {
  font-size: 10px;
  color: #333;
  font-weight: 600;
  margin-bottom: 4px;
}

.growth-change {
  font-size: 9px;
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 2px;
}

.growth-change.up {
  color: #10b981;
  background: #d1fae5;
}

.growth-change.down {
  color: #ef4444;
  background: #fee2e2;
}

.growth-change.stable {
  color: #6b7280;
  background: #f3f4f6;
}

/* 历史总览区域 */
.historical-section {
  margin: 48rpx 24rpx 64rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.historical-content {
  padding: 0 16px 20px 16px;
}

.historical-overview {
  margin-bottom: 24px;
}

.overview-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.overview-item {
  flex: 1;
  min-width: calc(50% - 6px);
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 12px;
  gap: 12px;
}

.overview-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 50%;
}

.overview-content {
  flex: 1;
}

.overview-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.overview-label {
  font-size: 12px;
  color: #666;
}

/* 最佳记录 */
.best-records {
  margin-bottom: 24px;
}

.records-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.records-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  gap: 12px;
}

.record-icon {
  font-size: 20px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 50%;
}

.record-content {
  flex: 1;
}

.record-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.record-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.record-date {
  font-size: 10px;
  color: #999;
}

/* 里程碑 */
.milestones-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.milestones-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.milestone-item {
  flex: 1;
  min-width: calc(50% - 4px);
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
}

.milestone-number {
  font-size: 18px;
  font-weight: 600;
  color: #4f46e5;
  margin-bottom: 4px;
}

.milestone-date {
  font-size: 12px;
  font-weight: 600;
  color: #4f46e5;
  margin-bottom: 4px;
}

.milestone-label {
  font-size: 11px;
  color: #666;
}

/* 成就区域 */
.achievements-section {
  margin-bottom: 32px;
}

.achievements-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  gap: 12px;
  transition: all 0.3s ease;
}

.achievement-item.unlocked {
  background: #d1fae5;
  border: 1px solid #10b981;
}

.achievement-item.locked {
  opacity: 0.5;
}

.achievement-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
}

.achievement-content {
  flex: 1;
}

.achievement-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.achievement-desc {
  font-size: 12px;
  color: #666;
}

.achievement-status {
  font-size: 16px;
  color: #10b981;
  font-weight: 600;
}

/* 收入调整统计入口 */
.income-adjustment-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  margin: 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.adjustment-entry {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.adjustment-entry:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.entry-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.entry-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.entry-content {
  flex: 1;
}

.entry-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.entry-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.entry-right {
  margin-left: 16rpx;
}

.entry-arrow {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 收入分解统计样式 */
.income-breakdown-section {
  margin: 32rpx 24rpx;
}

.breakdown-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 24rpx;
}

.breakdown-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.breakdown-card:active {
  transform: scale(0.98);
}

.breakdown-card.positive {
  border-left: 6rpx solid #22c55e;
}

.breakdown-card.negative {
  border-left: 6rpx solid #ef4444;
}

.breakdown-card.summary {
  border-left: 6rpx solid #3b82f6;
}

.breakdown-card .card-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 80rpx;
  text-align: center;
}

.breakdown-card .card-content {
  flex: 1;
}

.breakdown-card .card-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.breakdown-card .card-value.positive {
  color: #22c55e;
}

.breakdown-card .card-value.negative {
  color: #ef4444;
}

.breakdown-card .card-label {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.breakdown-card .card-subtitle {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}

/* 收入调整详细统计 */
.adjustment-details {
  margin-top: 32rpx;
}

.detail-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 24rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.type-stats {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.type-info {
  flex: 1;
}

.type-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4rpx;
}

.type-count {
  font-size: 24rpx;
  color: #718096;
}

.type-amount {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.type-amount.positive {
  color: #22c55e;
}

.type-amount.negative {
  color: #ef4444;
}

/* 底部间距 */
.bottom-spacing {
  height: 40px;
}
