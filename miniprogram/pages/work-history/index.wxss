page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 工作履历页面样式 */
.work-history-page {
  min-height: 100vh;
  background-color: #F5F7FB;
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.header-actions {
  margin-left: 20rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.add-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
  font-weight: 300;
}

/* 工作履历列表 */
.work-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #1a1a1a;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 现代化工作履历卡片 */
.modern-work-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.modern-work-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border-color: #e0e0e0;
}

.modern-work-card.current-work {
  border-left: 4rpx solid #4f46e5;
  background: linear-gradient(135deg, #fafaff 0%, #f8faff 100%);
}

.modern-work-card.current-work:hover {
  box-shadow: 0 8rpx 32rpx rgba(79, 70, 229, 0.12);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.card-header:active {
  background-color: #fafafa;
}

.header-left {
  flex: 1;
  min-width: 0;
}

/* 公司信息区域 */
.company-section {
  margin-bottom: 16rpx;
}

.company-name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 6rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
}

.current-indicator {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 4rpx 12rpx;
  background: #4f46e5;
  border-radius: 12rpx;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4rpx rgba(79, 70, 229, 0.1);
  }
}

.current-dot {
  width: 8rpx;
  height: 8rpx;
  background: #ffffff;
  border-radius: 50%;
  animation: pulse-dot 1.5s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.current-label {
  font-size: 20rpx;
  font-weight: 500;
  color: #ffffff;
}

.position-title {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.4;
}

/* 快速信息标签 */
.quick-info {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.info-chip {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.info-chip:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.chip-icon {
  font-size: 16rpx;
  line-height: 1;
}

.chip-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
  line-height: 1;
}

.salary-chip {
  background: #fef3e2;
  border-color: #fed7aa;
}

.salary-chip .chip-text {
  color: #ea580c;
}

.status-chip {
  position: relative;
}

.status-chip.status-active {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.status-chip.status-active .chip-text {
  color: #16a34a;
}

.status-chip.status-inactive {
  background: #fef2f2;
  border-color: #fecaca;
}

.status-chip.status-inactive .chip-text {
  color: #dc2626;
}

.status-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 4rpx;
}

.status-chip.status-active .status-indicator {
  background: #16a34a;
  animation: pulse-green 2s infinite;
}

.status-chip.status-inactive .status-indicator {
  background: #dc2626;
}

@keyframes pulse-green {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 现代化展开按钮 */
.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.expand-button:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: scale(1.05);
}

.expand-button.expanded {
  background: #4f46e5;
  border-color: #4f46e5;
}

.expand-button.expanded:hover {
  background: #4338ca;
  transform: scale(1.05);
}

/* 使用Unicode字符的展开图标 */
.expand-icon-text {
  font-size: 18rpx;
  color: #64748b;
  font-weight: normal;
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* 展开时旋转90度：▶ 变成 ▼ */
.expand-button.expanded .expand-icon-text {
  transform: rotate(90deg);
  color: #ffffff;
}

/* 卡片内容区域 */
.card-content {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fafbfc;
  border-top: 1rpx solid #f0f0f0;
}

.card-content.collapsed {
  max-height: 0;
  opacity: 0;
}

.card-content.expanded {
  max-height: 1000rpx;
  opacity: 1;
}

.content-inner {
  padding: 24rpx;
}

/* 详细信息网格 */
.details-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
  letter-spacing: 0.5rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 薪资行 */
.salary-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 16rpx;
  background: #ffffff;
  border: 1rpx solid #e5e7eb;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
}

.salary-row:last-child {
  margin-bottom: 0;
}

.salary-type {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.salary-amount {
  font-size: 26rpx;
  color: #059669;
  font-weight: 600;
}

/* 备注区域 */
.notes-section {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: #ffffff;
  border: 1rpx solid #e5e7eb;
  border-radius: 12rpx;
}

.notes-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8rpx;
}

.notes-content {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
}

/* 现代化操作按钮组 */
.action-group {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.modern-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border: none;
  border-radius: 10rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex: 1;
  min-width: 100rpx;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.modern-btn:active {
  transform: scale(0.96);
}

.btn-icon {
  font-size: 18rpx;
  line-height: 1;
}

.btn-text {
  font-size: 24rpx;
  line-height: 1;
}

/* 主要按钮 */
.primary-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(79, 70, 229, 0.25);
}

.primary-btn:hover {
  box-shadow: 0 6rpx 16rpx rgba(79, 70, 229, 0.35);
  transform: translateY(-1rpx);
}

/* 次要按钮 */
.secondary-btn {
  background: #ffffff;
  color: #4f46e5;
  border: 1rpx solid #e0e7ff;
  box-shadow: 0 2rpx 4rpx rgba(79, 70, 229, 0.08);
}

.secondary-btn:hover {
  background: #f8faff;
  border-color: #c7d2fe;
  box-shadow: 0 4rpx 8rpx rgba(79, 70, 229, 0.12);
}

/* 危险按钮 */
.danger-btn {
  background: #ffffff;
  color: #dc2626;
  border: 1rpx solid #fecaca;
  box-shadow: 0 2rpx 4rpx rgba(220, 38, 38, 0.08);
}

.danger-btn:hover {
  background: #fef2f2;
  border-color: #fca5a5;
  box-shadow: 0 4rpx 8rpx rgba(220, 38, 38, 0.12);
}

/* 模态框 */
@import "../../styles/modal-animations.wxss";

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;

  /* 使用统一的transition动画 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 32rpx;
  width: 100%;
  max-width: 640rpx;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20rpx);

  /* 使用统一的transition动画 */
  transform: scale(0.9) translateY(50rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show .modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 模态框头部样式更新 */
.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
}

.modal-title-section {
  flex: 1;
  margin-right: 32rpx;
}

.modal-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.modal-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
  line-height: 1.4;
}

.modal-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modal-close::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-close:active::before {
  width: 100%;
  height: 100%;
}

.modal-close:active {
  transform: scale(0.9);
}

.close-icon {
  font-size: 32rpx;
  color: #666;
  font-weight: 300;
}

.modal-body {
  flex: 1;
  /* max-height: 60vh; */
  overflow: auto; /* 添加溢出处理，确保不会超出父元素的容器宽度 */
  box-sizing: border-box; /* 确保内边距不会影响宽度计算 */
}

/* 表单章节头部样式 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid rgba(102, 126, 234, 0.1);
}

.section-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.section-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 24rpx;
  padding: 16rpx 20rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

/* 模态框底部样式优化 */
.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 36rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
}

.footer-btn {
  flex: 1;
  padding: 12rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 700;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 88rpx;
}

.footer-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-btn:active::before {
  width: 120%;
  height: 120%;
}

.footer-btn:active {
  transform: scale(0.96);
}

.cancel-btn {
  background: rgba(0, 0, 0, 0.04);
  color: #666;
  border: 2rpx solid rgba(0, 0, 0, 0.08);
}

.cancel-btn::before {
  background: rgba(0, 0, 0, 0.08);
}

.cancel-btn:active {
  background: rgba(0, 0, 0, 0.08);
  border-color: rgba(0, 0, 0, 0.12);
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
}

.save-btn::before {
  background: rgba(255, 255, 255, 0.2);
}

.save-btn:active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.5);
}

.btn-text {
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

/* 表单样式 */
.form-section {
  margin: 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

/* 表单标签行样式 */
.form-label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

/* 表单标签样式更新 */
.form-label {
  display: block;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 600;
}

/* 清除日期按钮样式 */
.clear-date-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(239, 68, 68, 0.1);
  border: 1rpx solid rgba(239, 68, 68, 0.2);
  border-radius: 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.clear-date-btn:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
  transform: scale(1.05);
}

.clear-date-btn:active {
  transform: scale(0.95);
  background: rgba(239, 68, 68, 0.2);
}

.clear-icon {
  font-size: 20rpx;
  color: #ef4444;
  margin-right: 6rpx;
  font-weight: 600;
}

.clear-text {
  font-size: 22rpx;
  color: #ef4444;
  font-weight: 500;
}

.required {
  color: #ef4444;
  font-weight: 500;
}

/* 输入框样式优化 */
.form-input {
  width: auto;
  padding: 24rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 20rpx;
  font-size: 30rpx;
  color: #1a1a1a;
  background: rgba(102, 126, 234, 0.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  font-weight: 500;
}

.form-input:focus {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.06);
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.form-input.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  box-shadow: 0 4rpx 20rpx rgba(239, 68, 68, 0.15);
}

/* 日期选择器样式优化 */
.form-picker {
  width: 100%;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 20rpx;
  background: rgba(102, 126, 234, 0.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.form-picker:active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.08);
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.form-picker.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  box-shadow: 0 4rpx 20rpx rgba(239, 68, 68, 0.15);
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 24rpx;
  position: relative;
}

.picker-text {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 500;
  flex: 1;
}

.picker-text.placeholder {
  color: #999;
  font-weight: 400;
}

.picker-arrow {
  font-size: 20rpx;
  color: #667eea;
  font-weight: 600;
  transform: rotate(0deg);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-picker:active .picker-arrow {
  transform: rotate(180deg);
}

/* 文本域样式优化 */
.form-textarea {
  width: auto;
  padding: 24rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 20rpx;
  font-size: 30rpx;
  color: #1a1a1a;
  background: rgba(102, 126, 234, 0.02);
  min-height: 160rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  font-weight: 500;
  resize: none;
}

.form-textarea:focus {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.06);
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.textarea-counter {
  display: flex;
  justify-content: flex-end;
  margin-top: 12rpx;
}

.counter-text {
  font-size: 22rpx;
  color: #999;
  font-weight: 400;
}

/* 错误文本样式优化 */
.error-text {
  display: block;
  color: #ef4444;
  font-size: 24rpx;
  margin-top: 12rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: rgba(239, 68, 68, 0.05);
  border-radius: 8rpx;
  border-left: 4rpx solid #ef4444;
}



/* 发薪日设置样式 */
.payday-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.payday-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.payday-info {
  flex: 1;
  margin-right: 15rpx;
}

.payday-name-input {
  width: 100%;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.payday-actions {
  display: flex;
  align-items: center;
}

.remove-btn {
  padding: 8rpx 12rpx;
  color: #ff6b6b;
  font-size: 24rpx;
  font-weight: bold;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 6rpx;
}

.payday-day-setting {
  display: flex;
  align-items: center;
  justify-content: center;
}

.payday-label {
  font-size: 28rpx;
  color: #666;
  margin: 0 10rpx;
}

.payday-day-picker {
  margin: 0 10rpx;
}

.payday-day-picker .picker-display {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8rpx;
  min-width: 80rpx;
  justify-content: center;
}

.payday-day-picker .picker-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
  margin-right: 8rpx;
}

.payday-day-picker .picker-arrow {
  font-size: 20rpx;
  color: #667eea;
}

.add-payday-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  margin-top: 15rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx dashed rgba(102, 126, 234, 0.3);
  border-radius: 12rpx;
  color: #667eea;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.add-text {
  font-size: 28rpx;
  font-weight: 500;
}