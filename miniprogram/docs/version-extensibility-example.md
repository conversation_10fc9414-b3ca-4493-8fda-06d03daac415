# 版本功能扩展性示例

## 设计优势

通过直接传递完整的 `event` 对象给云函数API，任何API都可以轻松获取版本信息和其他全局参数，无需修改云函数入口文件。

## 实现方式

### 1. 云函数入口统一传递 event

```javascript
// cloudfunctions/cloud-functions/index.js
exports.main = async (event, context) => {
  const { type } = event
  
  switch (type) {
    case "getUserInfo":
      return await getUserInfo(event)  // 直接传递完整 event
    case "updateUserInfo":
      return await updateUserInfo(event)  // 直接传递完整 event
    case "checkIn":
      return await checkIn(event)  // 直接传递完整 event
    // 所有API都使用相同的模式
  }
}
```

### 2. API函数中获取版本信息

```javascript
// 任何API函数都可以这样获取版本信息
exports.someApiFunction = wrapAsync(async (event = {}) => {
  const { data = {}, version, type } = event
  
  // 使用版本信息进行业务逻辑
  if (version) {
    console.log(`[${type}] 版本 ${version} 的用户调用API`)
    
    // 可以根据版本做不同处理
    if (compareVersion(version, '1.1.0') >= 0) {
      // 新版本功能
    } else {
      // 老版本功能
    }
  }
  
  // 正常的业务逻辑
  // ...
})
```

## 实际应用示例

### 1. 用户信息相关API

```javascript
// 获取用户信息 - 记录注册版本和更新当前版本
exports.getUserInfo = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  const result = await ensureUserExists({ ...data, version })
  
  if (result.isNewUser) {
    // 新用户：记录注册版本
    console.log(`[新用户注册] 版本: ${version}`)
  } else {
    // 老用户：更新当前版本
    if (version && result.data.version?.currentVersion !== version) {
      await updateUserVersion(result.data.openid, version)
      console.log(`[版本更新] 用户 ${result.data.openid} 版本: ${version}`)
    }
  }
  
  return success(result.data)
})

// 更新用户信息 - 记录版本统计
exports.updateUserInfo = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  
  // 版本统计
  if (version) {
    console.log(`[用户更新] 版本 ${version} 的用户更新信息`)
  }
  
  // 正常业务逻辑
  const userResult = await getCurrentUser()
  // ...
})
```

### 2. 签到相关API

```javascript
// 签到功能 - 可以根据版本提供不同奖励
exports.checkIn = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  
  // 根据版本决定签到奖励
  let baseReward = 10
  if (version && compareVersion(version, '1.2.0') >= 0) {
    baseReward = 15  // 新版本用户获得更多奖励
    console.log(`[签到] 新版本用户 ${version} 获得额外奖励`)
  }
  
  // 执行签到逻辑
  // ...
})
```

### 3. 商店相关API

```javascript
// 购买商品 - 版本功能开关
exports.purchaseItem = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  const { itemId } = data
  
  // 检查版本功能开关
  if (itemId === 'premium_item' && version) {
    if (compareVersion(version, '1.3.0') < 0) {
      return error('此商品需要更新到最新版本才能购买')
    }
  }
  
  // 记录购买行为的版本信息
  console.log(`[购买] 版本 ${version} 用户购买商品 ${itemId}`)
  
  // 执行购买逻辑
  // ...
})
```

### 4. 反馈相关API

```javascript
// 提交反馈 - 自动记录版本信息
exports.submitFeedback = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  
  // 在反馈中自动记录版本信息
  const feedbackData = {
    ...data,
    version: version,  // 自动记录用户版本
    timestamp: new Date()
  }
  
  console.log(`[反馈] 版本 ${version} 用户提交反馈`)
  
  // 保存反馈
  // ...
})
```

## 扩展功能示例

### 1. 版本统计分析

```javascript
// 可以在任何API中收集版本统计
const versionStats = new Map()

function recordVersionUsage(apiType, version) {
  const key = `${apiType}_${version}`
  versionStats.set(key, (versionStats.get(key) || 0) + 1)
}

// 在API中使用
exports.someApi = wrapAsync(async (event = {}) => {
  const { version, type } = event
  
  if (version) {
    recordVersionUsage(type, version)
  }
  
  // API逻辑
})
```

### 2. 功能灰度发布

```javascript
// 根据版本控制功能开放
function isFeatureEnabled(featureName, version) {
  const featureVersions = {
    'advanced_analytics': '1.2.0',
    'premium_themes': '1.3.0',
    'ai_assistant': '1.4.0'
  }
  
  const requiredVersion = featureVersions[featureName]
  if (!requiredVersion || !version) return false
  
  return compareVersion(version, requiredVersion) >= 0
}

// 在API中使用
exports.getAdvancedAnalytics = wrapAsync(async (event = {}) => {
  const { version } = event
  
  if (!isFeatureEnabled('advanced_analytics', version)) {
    return error('此功能需要更新到最新版本')
  }
  
  // 返回高级分析数据
})
```

### 3. 版本兼容性处理

```javascript
// 根据版本返回不同格式的数据
exports.getDataList = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  
  const result = await getDataFromDB(data)
  
  // 根据版本返回不同格式
  if (version && compareVersion(version, '1.1.0') >= 0) {
    // 新版本格式：包含更多字段
    return success({
      items: result.items,
      pagination: result.pagination,
      metadata: result.metadata
    })
  } else {
    // 老版本格式：简化数据
    return success({
      items: result.items.map(item => ({
        id: item.id,
        name: item.name
      }))
    })
  }
})
```

## 优势总结

1. **零配置扩展**：新API自动获得版本信息，无需修改入口文件
2. **统一接口**：所有API使用相同的参数结构
3. **灵活应用**：可以根据业务需要选择性使用版本信息
4. **易于维护**：版本相关逻辑集中在各自的API函数中
5. **功能丰富**：支持版本统计、功能开关、兼容性处理等

这种设计让版本功能具有极强的扩展性，任何新增的API都能轻松获取和使用版本信息。
