# 版本参数结构说明

## 参数结构变更

根据需求，版本信息现在放在云函数调用的顶层，与 `type` 和 `data` 同级。

## 新的参数结构

### 云函数调用结构
```javascript
// 小程序端调用
api.call('getUserInfo', { userId: 'test123' })

// 实际发送到云函数的数据结构
{
  type: 'getUserInfo',           // API类型
  data: {                        // 业务数据
    userId: 'test123'
  },
  version: '1.0.0'              // 版本信息（顶层参数）
}
```

### 云函数接收结构
```javascript
// 云函数 index.js 中的 event 对象
exports.main = async (event, context) => {
  console.log('接收到的参数:', event)
  // {
  //   type: 'getUserInfo',
  //   data: { userId: 'test123' },
  //   version: '1.0.0'
  // }
  
  const { type, data, version } = event
  
  switch (type) {
    case 'getUserInfo':
      // 将版本信息传递给具体的API函数
      return await getUserInfo({
        ...data,
        _version: version  // 通过 _version 传递版本信息
      })
  }
}
```

### API函数中的版本处理
```javascript
// cloudfunctions/cloud-functions/api/user.js
exports.getUserInfo = wrapAsync(async (params = {}) => {
  // 从参数中获取版本信息
  const currentVersion = params._version
  
  if (result.isNewUser) {
    // 新用户：设置注册版本和当前版本
    // 版本信息已在 createUser 中处理
  } else {
    // 老用户：更新当前版本
    if (currentVersion) {
      await usersDB.updateByOpenid(result.data.openid, {
        'version.currentVersion': currentVersion,
        'version.lastVersionUpdateAt': new Date()
      })
    }
  }
})
```

## 优势

### 1. 清晰的参数分层
- `type`: API类型标识
- `data`: 业务数据
- `version`: 版本信息

### 2. 易于扩展
可以在顶层添加更多全局参数，如：
```javascript
{
  type: 'getUserInfo',
  data: { userId: 'test123' },
  version: '1.0.0',
  platform: 'miniprogram',    // 平台信息
  deviceId: 'xxx',            // 设备标识
  timestamp: 1704672000000    // 请求时间戳
}
```

### 3. 统一处理
所有API都能统一获取版本信息，便于：
- 版本统计
- 兼容性处理
- 功能开关控制

## 实现细节

### 小程序端自动添加
```javascript
// miniprogram/core/api/base.js
async callCloudFunction(type, data = {}) {
  const result = await wx.cloud.callFunction({
    name: this.cloudFunctionName,
    data: {
      type,
      data,
      version: getVersion()  // 自动添加版本信息
    }
  })
}
```

### 云函数统一分发
```javascript
// cloudfunctions/cloud-functions/index.js
exports.main = async (event, context) => {
  const { type, data, version } = event
  
  switch (type) {
    case "getUserInfo":
      return await getUserInfo({
        ...data,
        _version: version  // 传递版本信息
      })
    // 其他API也可以类似处理
  }
}
```

### 数据库存储
```javascript
// 用户表中的版本字段
{
  version: {
    registrationVersion: '1.0.0',    // 注册版本
    currentVersion: '1.2.0',         // 当前版本
    lastVersionUpdateAt: Date        // 最后更新时间
  }
}
```

## 注意事项

1. **参数命名**: 在API函数内部使用 `_version` 来避免与业务数据中的 `version` 字段冲突
2. **向后兼容**: 老用户首次使用时会自动补充版本信息
3. **错误处理**: 如果版本信息缺失，不应影响正常业务逻辑
4. **日志记录**: 建议在关键位置记录版本信息，便于问题排查

## 测试验证

运行测试确保参数结构正确：
```bash
cd miniprogram && node test/version-test.js
```

测试会验证：
- 版本信息正确添加到顶层
- 云函数能正确接收版本参数
- 用户版本信息正确处理
