# 版本追踪功能使用指南

## 功能概述

版本追踪功能用于记录用户的注册版本和当前使用版本，帮助进行用户行为分析和版本统计。

## 功能特性

- **自动版本参数添加**：所有API请求自动携带当前小程序版本
- **注册版本记录**：用户注册时记录注册版本，永不修改
- **当前版本更新**：用户每次登录时更新当前使用版本
- **版本比较工具**：提供版本号比较功能
- **向后兼容**：对老用户自动补充版本信息

## 实现架构

### 1. 小程序端版本配置

```javascript
// miniprogram/core/config/version.js
const APP_VERSION = '1.0.0'

// 获取当前版本
const version = getVersion()

// 获取完整版本信息
const versionInfo = getVersionInfo()
```

### 2. API自动版本参数

所有通过 `BaseApiClient` 发起的请求都会自动添加版本参数：

```javascript
// 原始调用
api.call('getUserInfo', { userId: 'xxx' })

// 实际发送到云函数的数据结构
{
  type: 'getUserInfo',
  data: {
    userId: 'xxx'
  },
  version: '1.0.0'  // 版本信息在顶层，与 type、data 同级
}
```

### 3. 云函数版本处理

云函数接收到版本参数后，会根据用户状态进行处理：

**新用户注册：**
```javascript
{
  version: {
    registrationVersion: '1.0.0',  // 注册版本
    currentVersion: '1.0.0',       // 当前版本
    lastVersionUpdateAt: Date      // 最后更新时间
  }
}
```

**老用户登录：**
- 更新 `currentVersion` 为最新版本
- 保持 `registrationVersion` 不变
- 更新 `lastVersionUpdateAt`

## 使用示例

### 1. 获取版本信息

```javascript
const { getVersion, getVersionInfo } = require('./core/config/version.js')

// 获取版本号
const version = getVersion()  // '1.0.0'

// 获取完整信息
const info = getVersionInfo()
// {
//   version: '1.0.0',
//   date: '2025-01-08',
//   description: '初始版本，包含基础时间跟踪功能',
//   timestamp: 1704672000000
// }
```

### 2. 版本比较

```javascript
const { compareVersion } = require('./core/config/version.js')

compareVersion('1.0.0', '1.0.1')  // -1 (小于)
compareVersion('1.0.1', '1.0.0')  //  1 (大于)
compareVersion('1.0.0', '1.0.0')  //  0 (等于)
```

### 3. API调用（自动添加版本）

```javascript
// 使用任何API都会自动添加版本参数
import { api } from './core/api/index.js'

// 这些调用都会自动携带版本信息
await api.call('getUserInfo')
await api.user.getUserInfo()
await api.checkIn.checkIn()
```

## 数据库结构

### 用户表版本字段

```javascript
{
  _id: ObjectId,
  openid: String,
  // ... 其他字段
  version: {
    registrationVersion: String,    // 注册时的版本，如 '1.0.0'
    currentVersion: String,         // 当前使用版本，如 '1.2.0'
    lastVersionUpdateAt: Date       // 版本最后更新时间
  }
}
```

## 版本更新流程

### 发布新版本时

1. 更新 `miniprogram/core/config/version.js` 中的版本号
2. 更新版本描述和发布日期
3. 发布小程序

### 用户使用新版本时

1. 小程序启动，加载新版本号
2. 用户调用任何API（如获取用户信息）
3. API自动携带新版本号
4. 云函数更新用户的 `currentVersion`
5. 保持 `registrationVersion` 不变

## 统计分析应用

### 1. 版本分布统计

```javascript
// 云函数中统计各版本用户数量
db.collection('users').aggregate([
  {
    $group: {
      _id: '$version.currentVersion',
      count: { $sum: 1 }
    }
  }
])
```

### 2. 新老用户分析

```javascript
// 统计不同注册版本的用户活跃度
db.collection('users').aggregate([
  {
    $group: {
      _id: '$version.registrationVersion',
      totalUsers: { $sum: 1 },
      activeUsers: {
        $sum: {
          $cond: [
            { $eq: ['$version.currentVersion', '1.0.0'] },
            1, 0
          ]
        }
      }
    }
  }
])
```

## 注意事项

1. **版本号格式**：建议使用语义化版本号（如 1.0.0）
2. **向后兼容**：老用户首次使用新版本时会自动补充版本信息
3. **性能影响**：版本参数很小，对性能影响微乎其微
4. **数据一致性**：确保版本号在配置文件中正确设置

## 测试验证

运行版本功能测试：

```javascript
// 在小程序开发工具控制台中运行
const versionTest = require('./test/version-test.js')
versionTest.runAllTests()
```

## 故障排除

### 常见问题

1. **版本参数未添加**
   - 检查 `version.js` 文件是否正确导入
   - 确认 `getVersion()` 函数返回正确值

2. **云函数版本处理失败**
   - 检查云函数日志
   - 确认数据库字段结构正确

3. **老用户版本信息缺失**
   - 正常现象，首次使用新版本时会自动补充
