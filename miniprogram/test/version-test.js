/**
 * 版本功能测试
 * 测试版本配置和API调用中的版本参数
 */

const { getVersion, getVersionInfo, compareVersion } = require('../core/config/version.js')

/**
 * 测试版本配置功能
 */
function testVersionConfig() {
  console.log('=== 版本配置测试 ===')
  
  try {
    // 测试获取版本号
    const version = getVersion()
    console.log('当前版本:', version)
    console.assert(typeof version === 'string', '版本号应该是字符串')
    console.assert(version.length > 0, '版本号不能为空')
    
    // 测试获取版本信息
    const versionInfo = getVersionInfo()
    console.log('版本信息:', versionInfo)
    console.assert(typeof versionInfo === 'object', '版本信息应该是对象')
    console.assert(versionInfo.version === version, '版本信息中的版本号应该一致')
    console.assert(typeof versionInfo.date === 'string', '版本日期应该是字符串')
    console.assert(typeof versionInfo.description === 'string', '版本描述应该是字符串')
    console.assert(typeof versionInfo.timestamp === 'number', '时间戳应该是数字')
    
    // 测试版本比较
    console.log('版本比较测试:')
    console.assert(compareVersion('1.0.0', '1.0.0') === 0, '相同版本比较应该返回0')
    console.assert(compareVersion('1.0.0', '1.0.1') === -1, '低版本比较应该返回-1')
    console.assert(compareVersion('1.0.1', '1.0.0') === 1, '高版本比较应该返回1')
    console.assert(compareVersion('1.1.0', '1.0.9') === 1, '次版本号比较测试')
    console.assert(compareVersion('2.0.0', '1.9.9') === 1, '主版本号比较测试')
    
    console.log('✅ 版本配置测试通过')
    return true
  } catch (error) {
    console.error('❌ 版本配置测试失败:', error)
    return false
  }
}

/**
 * 模拟测试API调用中的版本参数
 */
function testApiVersionParameter() {
  console.log('=== API版本参数测试 ===')
  
  try {
    // 模拟 BaseApiClient 的行为
    const { getVersion } = require('../core/config/version.js')
    
    // 模拟原始请求数据
    const originalData = {
      userId: 'test123',
      action: 'getUserInfo'
    }
    
    // 模拟添加版本参数的过程（版本信息在顶层）
    const cloudFunctionData = {
      type: 'getUserInfo',
      data: originalData,
      version: getVersion()
    }

    console.log('原始数据:', originalData)
    console.log('云函数调用数据:', cloudFunctionData)

    // 验证版本参数是否正确添加
    console.assert(cloudFunctionData.version === getVersion(), '版本参数应该正确添加')
    console.assert(cloudFunctionData.data.userId === originalData.userId, '原始数据应该保持不变')
    console.assert(cloudFunctionData.data.action === originalData.action, '原始数据应该保持不变')
    
    console.log('✅ API版本参数测试通过')
    return true
  } catch (error) {
    console.error('❌ API版本参数测试失败:', error)
    return false
  }
}

/**
 * 模拟测试用户版本信息处理
 */
function testUserVersionHandling() {
  console.log('=== 用户版本信息处理测试 ===')
  
  try {
    const currentVersion = getVersion()
    
    // 模拟新用户数据（版本信息直接传递）
    const newUserData = {
      openid: 'test_openid_123',
      nickname: '测试用户',
      version: currentVersion
    }

    // 模拟创建用户时的版本字段
    const userVersionData = {
      registrationVersion: newUserData.version,
      currentVersion: newUserData.version,
      lastVersionUpdateAt: new Date()
    }
    
    console.log('新用户版本数据:', userVersionData)
    
    // 验证版本数据
    console.assert(userVersionData.registrationVersion === currentVersion, '注册版本应该正确设置')
    console.assert(userVersionData.currentVersion === currentVersion, '当前版本应该正确设置')
    console.assert(userVersionData.lastVersionUpdateAt instanceof Date, '更新时间应该是Date对象')
    
    // 模拟老用户版本更新
    const oldUserVersion = { registrationVersion: '0.9.0', currentVersion: '0.9.0' }
    const newVersion = currentVersion
    
    if (oldUserVersion.currentVersion !== newVersion) {
      const versionUpdateData = {
        'version.currentVersion': newVersion,
        'version.lastVersionUpdateAt': new Date()
      }
      
      console.log('老用户版本更新数据:', versionUpdateData)
      console.assert(versionUpdateData['version.currentVersion'] === newVersion, '当前版本应该更新')
    }
    
    console.log('✅ 用户版本信息处理测试通过')
    return true
  } catch (error) {
    console.error('❌ 用户版本信息处理测试失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始运行版本功能测试...\n')
  
  const results = [
    testVersionConfig(),
    testApiVersionParameter(),
    testUserVersionHandling()
  ]
  
  const passedCount = results.filter(result => result).length
  const totalCount = results.length
  
  console.log(`\n测试结果: ${passedCount}/${totalCount} 通过`)
  
  if (passedCount === totalCount) {
    console.log('🎉 所有测试通过！版本功能实现正确。')
  } else {
    console.log('⚠️  部分测试失败，请检查实现。')
  }
  
  return passedCount === totalCount
}

// 导出测试函数
module.exports = {
  testVersionConfig,
  testApiVersionParameter,
  testUserVersionHandling,
  runAllTests
}

// 如果直接运行此文件，执行所有测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests()
}
