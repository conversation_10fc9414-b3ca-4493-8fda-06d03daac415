<!--收入调整模态框组件-->
<view class="modal-overlay {{visible ? 'show' : ''}}" bindtap="onOverlayTap">
  <view class="modal-container" catchtap="onModalTap">
    <!-- 模态框头部 -->
    <view class="modal-header">
      <text class="modal-title">
        {{isEdit ? (mode === 'income' ? '编辑额外收入' : '编辑扣款') : (mode === 'income' ? '添加额外收入' : '添加扣款')}}
      </text>
      <text class="close-btn" bindtap="onClose">✕</text>
    </view>

    <!-- 模态框内容 -->
    <view class="modal-content">
      <!-- 类型输入 -->
      <view class="form-group">
        <text class="form-label">类型</text>
        <input
          class="form-input {{errors.type ? 'error' : ''}}"
          placeholder="{{mode === 'income' ? '请输入收入类型，如：销售提成' : '请输入扣款类型，如：迟到扣款'}}"
          value="{{formData.type}}"
          bindinput="onTypeInput"
        />
        <text wx:if="{{errors.type}}" class="error-text">{{errors.type}}</text>
      </view>

      <!-- 金额输入 -->
      <view class="form-group">
        <text class="form-label">金额</text>
        <input 
          class="form-input {{errors.amount ? 'error' : ''}}"
          type="digit"
          placeholder="请输入金额"
          value="{{formData.amount}}"
          bindinput="onAmountInput"
        />
        <text wx:if="{{errors.amount}}" class="error-text">{{errors.amount}}</text>
      </view>

      <!-- 描述输入 -->
      <view class="form-group">
        <text class="form-label">描述 <text class="optional-text">(可选)</text></text>
        <input
          class="form-input {{errors.description ? 'error' : ''}}"
          placeholder="{{mode === 'income' ? '可输入收入描述，如：销售提成' : '可输入扣款原因，如：迟到30分钟'}}"
          value="{{formData.description}}"
          bindinput="onDescriptionInput"
        />
        <text wx:if="{{errors.description}}" class="error-text">{{errors.description}}</text>
      </view>

      <!-- 常见类型快捷选择 -->
      <view class="form-group" wx:if="{{commonTypes.length > 0}}">
        <text class="form-label">常见类型</text>
        <view class="preset-tags">
          <text
            wx:for="{{commonTypes}}"
            wx:key="index"
            class="preset-tag"
            bindtap="onCommonTypeTap"
            data-type="{{item}}"
          >
            {{item}}
          </text>
        </view>
      </view>
    </view>

    <!-- 模态框底部 -->
    <view class="modal-footer">
      <button wx:if="{{isEdit}}" class="btn btn-delete" bindtap="onDelete">删除</button>
      <button wx:else class="btn btn-cancel" bindtap="onClose">取消</button>
      <button
        class="btn btn-confirm {{loading ? 'loading' : ''}}"
        bindtap="onConfirm"
        disabled="{{loading}}"
      >
        {{loading ? '保存中...' : (isEdit ? '保存' : '确定')}}
      </button>
    </view>
  </view>
</view>
