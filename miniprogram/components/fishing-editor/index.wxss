/* 摸鱼记录编辑器样式 */
@import "../../styles/modal-animations.wxss";

.fishing-editor {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 20rpx;
  box-sizing: border-box;
}

.fishing-editor.show {
  opacity: 1;
  visibility: visible;
}

.editor-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 90%;
  max-width: 640rpx;
  max-height: 80vh;
  margin: 20rpx 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.fishing-editor.show .editor-content {
  transform: scale(1);
}

/* 标题栏 */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.editor-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e6e6e6;
  transform: scale(0.95);
}

.close-icon {
  font-size: 24rpx;
  color: #666;
}

/* 表单内容 */
.editor-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

/* 时间范围选择器 */
.time-range-selector {
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 8rpx 24rpx 8rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 88rpx;
}

.time-range-selector:active {
  background: #e2e8f0;
  transform: scale(0.98);
}

.time-range-display {
  flex: 1;
}

.time-range-text {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.start-time, .end-time {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.time-separator {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

.start-next-day-indicator,
.end-next-day-indicator {
  background: #fef3c7;
  color: #d97706;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-weight: 600;
  margin-left: 8rpx;
}

.duration-text {
  font-size: 24rpx;
  color: #515151;
  background-color: #e8e9ec;
}

.time-range-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #94a3b8;
  font-weight: bold;
}

/* 备注输入 */
.remark-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.remark-input:focus {
  border-color: #1890ff;
  outline: none;
}

.remark-input::placeholder {
  color: #bfbfbf;
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 错误信息 */
.error-text {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 8rpx;
}

/* 操作按钮 */
.editor-footer {
  display: flex;
  padding: 24rpx 32rpx 32rpx;
  gap: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.editor-btn {
  flex: 1;
  padding: 16rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e6e6e6;
}

.delete-btn {
  background: #dc2626;
  color: #fff;
}

.delete-btn:active {
  background: #b91c1c;
}

.save-btn {
  background: #1890ff;
  color: #fff;
}

.save-btn:not(:disabled):active {
  background: #096dd9;
}

.save-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .editor-content {
    width: 95vw;
  }
  
  .editor-body {
    padding: 24rpx;
  }
  
  .editor-footer {
    padding: 16rpx 24rpx 24rpx;
  }
}
