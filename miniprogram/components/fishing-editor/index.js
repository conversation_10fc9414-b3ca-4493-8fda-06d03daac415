/**
 * 摸鱼记录编辑器组件
 * 支持添加和编辑摸鱼记录
 */

const { minutesToTimeDisplay, timeStringToMinutes, formatDuration } = require('../../utils/helpers/time-utils.js')

Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    // 是否显示编辑器
    visible: {
      type: Boolean,
      value: false
    },
    // 编辑模式：add（添加）、edit（编辑）
    mode: {
      type: String,
      value: 'add'
    },
    // 编辑的摸鱼数据（编辑模式时使用）
    fishingData: {
      type: Object,
      value: null
    },
    // 工作时间段数据（用于验证摸鱼时间）
    segments: {
      type: Array,
      value: []
    }
  },

  data: {
    // 表单数据
    formData: {
      startTime: '',
      endTime: '',
      startIsNextDay: false,
      endIsNextDay: false,
      remark: ''
    },

    // 验证错误
    errors: {
      startTime: '',
      endTime: '',
      timeRange: '',
      workSegment: ''
    },

    // 界面状态
    loading: false,

    // 时间范围选择器状态
    showTimeRangePicker: false,
    timeRangeDuration: ''
  },

  observers: {
    'visible,mode,fishingData': function(visible, mode, fishingData) {
      if (visible) {
        this.initFormData()
      }
    },

    'formData.startTime, formData.endTime, formData.startIsNextDay, formData.endIsNextDay': function() {
      this.calculateTimeRangeDuration()
    }
  },

  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      if (this.data.mode === 'edit' && this.data.fishingData) {
        // 编辑模式，填充现有数据
        const fishing = this.data.fishingData
        this.setData({
          formData: {
            startTime: minutesToTimeDisplay(fishing.start),
            endTime: minutesToTimeDisplay(fishing.end),
            startIsNextDay: fishing.start >= 1440, // 大于等于24小时表示次日
            endIsNextDay: fishing.end >= 1440,
            remark: fishing.remark || ''
          }
        })
      } else {
        // 添加模式，清空表单
        this.setData({
          formData: {
            startTime: '',
            endTime: '',
            startIsNextDay: false,
            endIsNextDay: false,
            remark: ''
          }
        })
      }

      // 清空错误信息
      this.clearErrors()
    },

    /**
     * 清空错误信息
     */
    clearErrors() {
      this.setData({
        errors: {
          startTime: '',
          endTime: '',
          timeRange: '',
          workSegment: ''
        }
      })
    },

    /**
     * 打开时间范围选择器
     */
    onOpenTimeRangePicker() {
      console.log('摸鱼记录编辑器 - 打开时间范围选择器')
      this.setData({
        showTimeRangePicker: true
      })
    },

    /**
     * 时间范围选择器 - 确认选择
     */
    onTimeRangePickerConfirm(e) {
      const { startTime, endTime, isStartNextDay, isEndNextDay, duration } = e.detail

      console.log('摸鱼记录编辑器 - 时间范围选择器确认:', { startTime, endTime, isStartNextDay, isEndNextDay, duration })

      this.setData({
        'formData.startTime': startTime,
        'formData.endTime': endTime,
        'formData.startIsNextDay': isStartNextDay,
        'formData.endIsNextDay': isEndNextDay,
        'timeRangeDuration': duration,
        'showTimeRangePicker': false,
        'errors.startTime': '',
        'errors.endTime': '',
        'errors.timeRange': ''
      })
    },

    /**
     * 时间范围选择器 - 取消选择
     */
    onTimeRangePickerCancel() {
      console.log('摸鱼记录编辑器 - 时间范围选择器取消')
      this.setData({
        showTimeRangePicker: false
      })
    },

    /**
     * 时间范围选择器 - 关闭
     */
    onTimeRangePickerClose() {
      console.log('摸鱼记录编辑器 - 时间范围选择器关闭')
      this.setData({
        showTimeRangePicker: false
      })
    },

    /**
     * 备注输入
     */
    onRemarkInput(e) {
      this.setData({
        'formData.remark': e.detail.value
      })
    },

    /**
     * 计算时间范围时长
     */
    calculateTimeRangeDuration() {
      const { formData } = this.data

      if (!formData.startTime || !formData.endTime) {
        this.setData({ timeRangeDuration: '' })
        return
      }

      try {
        // 解析时间
        const [startHour, startMinute] = formData.startTime.split(':').map(Number)
        const [endHour, endMinute] = formData.endTime.split(':').map(Number)

        // 转换为分钟，考虑次日标记
        let startMinutes = startHour * 60 + startMinute
        let endMinutes = endHour * 60 + endMinute

        if (formData.startIsNextDay) {
          startMinutes += 24 * 60
        }
        if (formData.endIsNextDay) {
          endMinutes += 24 * 60
        }

        // 计算时长
        let durationMinutes = endMinutes - startMinutes

        if (durationMinutes <= 0) {
          this.setData({ timeRangeDuration: '' })
          return
        }

        // 转换为小时和分钟
        const hours = Math.floor(durationMinutes / 60)
        const minutes = durationMinutes % 60

        let duration = ''
        if (hours > 0) {
          duration += `${hours}小时`
        }
        if (minutes > 0) {
          duration += `${minutes}分钟`
        }
        if (duration === '') {
          duration = '0分钟'
        }

        this.setData({ timeRangeDuration: duration })
      } catch (error) {
        console.error('计算时长失败:', error)
        this.setData({ timeRangeDuration: '' })
      }
    },

    /**
     * 验证表单数据
     */
    validateForm() {
      const { formData } = this.data
      const errors = {}
      let isValid = true

      // 验证开始时间
      if (!formData.startTime) {
        errors.startTime = '请输入开始时间'
        isValid = false
      } else if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(formData.startTime)) {
        errors.startTime = '时间格式不正确'
        isValid = false
      }

      // 验证结束时间
      if (!formData.endTime) {
        errors.endTime = '请输入结束时间'
        isValid = false
      } else if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(formData.endTime)) {
        errors.endTime = '时间格式不正确'
        isValid = false
      }

      // 验证时间范围
      if (formData.startTime && formData.endTime) {
        const startMinutes = timeStringToMinutes(formData.startTime, formData.startIsNextDay)
        const endMinutes = timeStringToMinutes(formData.endTime, formData.endIsNextDay)

        if (startMinutes >= endMinutes) {
          errors.timeRange = '结束时间必须大于开始时间'
          isValid = false
        }

        // 验证是否在工作时间段内
        if (isValid) {
          const workSegmentValidation = this.validateWorkSegment(startMinutes, endMinutes)
          if (!workSegmentValidation.isValid) {
            errors.workSegment = workSegmentValidation.message
            isValid = false
          }
        }
      }

      this.setData({ errors })
      return isValid
    },

    /**
     * 验证摸鱼时间是否在工作时间段内
     */
    validateWorkSegment(startMinutes, endMinutes) {
      const { segments } = this.data

      if (!segments || segments.length === 0) {
        return {
          isValid: false,
          message: '没有工作时间段，无法添加摸鱼记录'
        }
      }

      // 查找包含摸鱼时间的工作时间段
      const containingSegment = segments.find(segment => {
        return segment.type !== 'rest' && 
               startMinutes >= segment.start && 
               endMinutes <= segment.end
      })

      if (!containingSegment) {
        return {
          isValid: false,
          message: '摸鱼时间必须完全在某个工作时间段内'
        }
      }

      return { isValid: true }
    },

    /**
     * 保存摸鱼记录
     */
    onSave() {
      if (this.data.loading) return

      if (!this.validateForm()) {
        return
      }

      this.setData({ loading: true })

      try {
        const { formData } = this.data
        const startMinutes = timeStringToMinutes(formData.startTime, formData.startIsNextDay)
        const endMinutes = timeStringToMinutes(formData.endTime, formData.endIsNextDay)

        const fishingData = {
          start: startMinutes,
          end: endMinutes,
          remark: formData.remark || ''
        }

        // 如果是编辑模式，添加ID
        if (this.data.mode === 'edit' && this.data.fishingData) {
          fishingData.id = this.data.fishingData.id
        }

        // 触发保存事件
        this.triggerEvent('save', {
          mode: this.data.mode,
          fishingData: fishingData
        })

        // 保存成功后关闭编辑器
        this.onClose()

      } catch (error) {
        console.error('保存摸鱼记录失败:', error)
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.setData({ loading: false })
      }
    },

    /**
     * 取消编辑
     */
    onCancel() {
      this.onClose()
    },

    /**
     * 删除摸鱼记录
     */
    onDelete() {
      if (this.data.mode !== 'edit' || !this.data.fishingData) {
        return
      }

      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条摸鱼记录吗？',
        confirmText: '删除',
        confirmColor: '#dc2626',
        success: (res) => {
          if (res.confirm) {
            // 触发删除事件
            this.triggerEvent('delete', {
              fishingData: this.data.fishingData
            })

            // 删除确认后关闭编辑器
            this.onClose()
          }
        }
      })
    },

    /**
     * 关闭编辑器
     */
    onClose() {
      this.triggerEvent('close')
    },

    /**
     * 阻止冒泡
     */
    onStopPropagation() {
      // 阻止事件冒泡
    }
  }
})
