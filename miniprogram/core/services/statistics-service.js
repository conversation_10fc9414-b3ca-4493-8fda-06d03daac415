/**
 * 统计服务类
 * 提供各种数据统计功能，确保只统计截止到当前时间的数据
 * 
 * 功能特性：
 * - 时间统计：工作时长、类型分布等
 * - 收入统计：总收入、平均收入、时薪统计等
 * - 摸鱼统计：摸鱼时长、频率分析等
 * - 工作履历统计：履历数量、在职时长等
 * - 时间截止控制：确保不包含未来的数据
 */

const { getDateStart, getDateEnd, formatDateKey } = require('../../utils/helpers/time-utils.js')

class StatisticsService {
  constructor() {
    // 获取数据管理器实例
    this.dataManager = getApp().getDataManager()

    // 缓存统计结果
    this.cache = new Map()
    this.cacheExpiry = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 获取当前时间（用于时间截止控制）
   * @returns {Date} 当前时间
   */
  getCurrentTime() {
    return new Date()
  }

  /**
   * 检查日期是否在截止时间之前
   * @param {Date} date - 要检查的日期
   * @param {Date} cutoffTime - 截止时间，默认为当前时间
   * @returns {boolean} 是否在截止时间之前
   */
  isDateBeforeCutoff(date, cutoffTime = null) {
    const cutoff = cutoffTime || this.getCurrentTime()
    const targetDate = getDateStart(date)
    const cutoffDate = getDateStart(cutoff)
    
    return targetDate <= cutoffDate
  }

  /**
   * 检查时间段是否在截止时间之前
   * @param {Date} date - 时间段所在日期
   * @param {Object} segment - 时间段对象
   * @param {Date} cutoffTime - 截止时间，默认为当前时间
   * @returns {boolean} 是否在截止时间之前
   */
  isSegmentBeforeCutoff(date, segment, cutoffTime = null) {
    const cutoff = cutoffTime || this.getCurrentTime()
    const segmentDate = getDateStart(date)
    const cutoffDate = getDateStart(cutoff)
    
    // 如果日期在截止日期之前，整个时间段都有效
    if (segmentDate < cutoffDate) {
      return true
    }
    
    // 如果日期在截止日期之后，整个时间段都无效
    if (segmentDate > cutoffDate) {
      return false
    }
    
    // 如果是同一天，需要检查时间段的开始时间
    const cutoffMinutes = cutoff.getHours() * 60 + cutoff.getMinutes()
    return segment.start <= cutoffMinutes
  }

  /**
   * 计算时间段在截止时间内的有效时长
   * @param {Date} date - 时间段所在日期
   * @param {Object} segment - 时间段对象
   * @param {Date} cutoffTime - 截止时间，默认为当前时间
   * @returns {number} 有效时长（分钟）
   */
  calculateValidDuration(date, segment, cutoffTime = null) {
    const cutoff = cutoffTime || this.getCurrentTime()
    const segmentDate = getDateStart(date)
    const cutoffDate = getDateStart(cutoff)
    
    // 如果日期在截止日期之前，整个时间段都有效
    if (segmentDate < cutoffDate) {
      return segment.end - segment.start
    }
    
    // 如果日期在截止日期之后，整个时间段都无效
    if (segmentDate > cutoffDate) {
      return 0
    }
    
    // 如果是同一天，需要计算截止时间内的有效时长
    const cutoffMinutes = cutoff.getHours() * 60 + cutoff.getMinutes()
    
    // 如果时间段开始时间就超过了截止时间，无效
    if (segment.start >= cutoffMinutes) {
      return 0
    }
    
    // 计算有效的结束时间
    const validEndTime = Math.min(segment.end, cutoffMinutes)
    return Math.max(0, validEndTime - segment.start)
  }

  /**
   * 直接获取指定工作履历的每日数据（不修改当前工作）
   * @param {string} workId - 工作履历ID
   * @returns {Object} 工作履历的每日数据
   */
  getWorkHistoryDailyData(workId) {
    try {
      const userData = this.dataManager.getUserData()
      if (!userData || !userData.workHistory) {
        return {}
      }

      const workHistory = userData.workHistory[workId]
      if (!workHistory) {
        console.warn(`工作履历 ${workId} 不存在`)
        return {}
      }

      // 数据存储在timeTracking字段中
      return workHistory.timeTracking || {}
    } catch (error) {
      console.error('获取工作履历每日数据失败:', error)
      return {}
    }
  }

  /**
   * 检查日期是否在指定范围内
   * @param {Date} date - 要检查的日期
   * @param {Array} dateRange - 日期范围 [startDate, endDate]
   * @returns {boolean} 是否在范围内
   */
  isDateInRange(date, dateRange) {
    if (!dateRange || dateRange.length !== 2) {
      return true
    }

    const [startDate, endDate] = dateRange
    return date >= startDate && date <= endDate
  }

  /**
   * 获取当前工作履历的每日数据（兼容原有调用）
   * @returns {Object} 当前工作履历的每日数据
   */
  getCurrentWorkDailyData() {
    try {
      const currentWork = this.dataManager.getCurrentWork()
      if (!currentWork) {
        // 如果没有当前工作，尝试获取第一个工作履历的数据（只读）
        const userData = this.dataManager.getUserData()
        const workHistoryIds = Object.keys(userData?.workHistory || {})
        if (workHistoryIds.length > 0) {
          console.log('没有当前工作，使用第一个工作履历的数据:', workHistoryIds[0])
          return this.getWorkHistoryDailyData(workHistoryIds[0])
        }
        return {}
      }

      return this.getWorkHistoryDailyData(currentWork.id)
    } catch (error) {
      console.error('获取当前工作每日数据失败:', error)
      return {}
    }
  }

  /**
   * 获取过滤后的日期数据（只包含截止时间之前的数据）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围 [startDate, endDate]，可选
   * @param {Date} cutoffTime - 截止时间，默认为当前时间
   * @returns {Object} 过滤后的日期数据
   */
  getFilteredDailyData(workId = null, dateRange = null, cutoffTime = null) {
    try {
      let allData

      if (workId) {
        // 直接获取指定工作履历的数据，不修改当前工作
        allData = this.getWorkHistoryDailyData(workId)
      } else {
        // 获取当前工作的数据
        allData = this.getCurrentWorkDailyData()
      }

      const cutoff = cutoffTime || this.getCurrentTime()
      const filteredData = {}
      
      Object.entries(allData).forEach(([dateKey, dayData]) => {
        const date = new Date(dateKey)

        // 检查日期范围
        if (dateRange && !this.isDateInRange(date, dateRange)) {
          return
        }

        // 检查是否在截止时间之前
        if (!this.isDateBeforeCutoff(date, cutoff)) {
          return
        }
        
        // 过滤时间段，只保留截止时间之前的有效部分
        const filteredSegments = []
        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            const validDuration = this.calculateValidDuration(date, segment, cutoff)
            if (validDuration > 0) {
              // 创建调整后的时间段
              const adjustedSegment = { ...segment }
              
              // 如果是当天且时间段被截断，调整结束时间
              const segmentDate = getDateStart(date)
              const cutoffDate = getDateStart(cutoff)
              if (segmentDate.getTime() === cutoffDate.getTime()) {
                const cutoffMinutes = cutoff.getHours() * 60 + cutoff.getMinutes()
                if (segment.end > cutoffMinutes) {
                  adjustedSegment.end = cutoffMinutes
                  // 按比例调整收入
                  const originalDuration = segment.end - segment.start
                  adjustedSegment.income = (segment.income || 0) * (validDuration / originalDuration)
                }
              }
              
              filteredSegments.push(adjustedSegment)
            }
          })
        }
        
        // 过滤摸鱼数据
        const filteredFishes = []
        if (dayData.fishes) {
          dayData.fishes.forEach(fish => {
            const validDuration = this.calculateValidDuration(date, fish, cutoff)
            if (validDuration > 0) {
              const adjustedFish = { ...fish }
              
              // 如果是当天且摸鱼被截断，调整结束时间
              const segmentDate = getDateStart(date)
              const cutoffDate = getDateStart(cutoff)
              if (segmentDate.getTime() === cutoffDate.getTime()) {
                const cutoffMinutes = cutoff.getHours() * 60 + cutoff.getMinutes()
                if (fish.end > cutoffMinutes) {
                  adjustedFish.end = cutoffMinutes
                }
              }
              
              filteredFishes.push(adjustedFish)
            }
          })
        }
        
        // 检查是否有收入调整数据
        const hasExtraIncome = dayData.extraIncomes && dayData.extraIncomes.length > 0
        const hasDeductions = dayData.deductions && dayData.deductions.length > 0



        // 只有当有有效数据时才添加到结果中（包括收入调整数据）
        if (filteredSegments.length > 0 || filteredFishes.length > 0 || hasExtraIncome || hasDeductions) {
          filteredData[dateKey] = {
            ...dayData,
            segments: filteredSegments,
            fishes: filteredFishes
          }

        }
      })

      return filteredData
    } catch (error) {
      console.error('获取过滤后的日期数据失败:', error)
      return {}
    }
  }

  /**
   * 计算概览统计
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 概览统计对象
   */
  calculateOverviewStatistics(workId = null, dateRange = null) {
    try {
      const cacheKey = `overview_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      let totalWorkMinutes = 0
      let totalIncome = 0
      let workDays = 0
      let totalSegments = 0

      Object.entries(filteredData).forEach(([dateKey, dayData]) => {
        // 使用时间跟踪管理器的动态计算方法
        const dayStats = this.dataManager.timeTrackingManager.getDayDataStats(dayData)
        const dayWorkMinutes = dayStats.totalWorkMinutes
        const dayIncome = dayStats.netIncome  // 净收入（包含收入调整）
        const daySegments = dayData.segments ? dayData.segments.filter(s => s.type !== 'rest').length : 0

        // 如果有工作时间或者有收入调整，都应该计入统计
        const hasIncomeAdjustment = (dayData.extraIncomes && dayData.extraIncomes.length > 0) ||
                                   (dayData.deductions && dayData.deductions.length > 0)

        if (dayWorkMinutes > 0 || hasIncomeAdjustment) {
          totalWorkMinutes += dayWorkMinutes
          totalIncome += dayIncome
          workDays++
        }

        totalSegments += daySegments
      })
      
      const result = {
        totalWorkHours: Math.round((totalWorkMinutes / 60) * 100) / 100,
        totalWorkMinutes: totalWorkMinutes,
        totalIncome: Math.round(totalIncome * 100) / 100,
        workDays: workDays,
        averageHourlyRate: totalWorkMinutes > 0 ? Math.round((totalIncome / (totalWorkMinutes / 60)) * 100) / 100 : 0,
        averageDailyIncome: workDays > 0 ? Math.round((totalIncome / workDays) * 100) / 100 : 0,
        averageDailyHours: workDays > 0 ? Math.round((totalWorkMinutes / 60 / workDays) * 100) / 100 : 0,
        totalSegments: totalSegments
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算概览统计失败:', error)
      return {
        totalWorkHours: 0,
        totalWorkMinutes: 0,
        totalIncome: 0,
        workDays: 0,
        averageHourlyRate: 0,
        averageDailyIncome: 0,
        averageDailyHours: 0,
        totalSegments: 0
      }
    }
  }

  /**
   * 缓存相关方法
   */
  getFromCache(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data
    }
    return null
  }

  setCache(key, data) {
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    })
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.cache.clear()
  }

  /**
   * 清除统计相关缓存
   */
  clearStatisticsCache() {
    // 清除所有包含 overview 或 comprehensive 的缓存键
    for (const key of this.cache.keys()) {
      if (key.includes('overview') || key.includes('comprehensive')) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 计算时间类型分布统计
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 时间类型分布统计
   */
  calculateTimeTypeStatistics(workId = null, dateRange = null) {
    try {
      const cacheKey = `timeType_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      const stats = {
        work: 0,      // 工作时间（分钟）
        rest: 0,      // 休息时间（分钟）
        overtime: 0   // 加班时间（分钟）
      }

      Object.values(filteredData).forEach(dayData => {
        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            const duration = segment.end - segment.start
            if (stats.hasOwnProperty(segment.type)) {
              stats[segment.type] += duration
            }
          })
        }
      })

      // 转换为小时并计算百分比
      const totalMinutes = stats.work + stats.rest + stats.overtime
      const result = {
        work: {
          minutes: stats.work,
          hours: Math.round((stats.work / 60) * 100) / 100,
          percentage: totalMinutes > 0 ? Math.round((stats.work / totalMinutes) * 10000) / 100 : 0
        },
        rest: {
          minutes: stats.rest,
          hours: Math.round((stats.rest / 60) * 100) / 100,
          percentage: totalMinutes > 0 ? Math.round((stats.rest / totalMinutes) * 10000) / 100 : 0
        },
        overtime: {
          minutes: stats.overtime,
          hours: Math.round((stats.overtime / 60) * 100) / 100,
          percentage: totalMinutes > 0 ? Math.round((stats.overtime / totalMinutes) * 10000) / 100 : 0
        },
        total: {
          minutes: totalMinutes,
          hours: Math.round((totalMinutes / 60) * 100) / 100
        }
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算时间类型分布统计失败:', error)
      return {
        work: { minutes: 0, hours: 0, percentage: 0 },
        rest: { minutes: 0, hours: 0, percentage: 0 },
        overtime: { minutes: 0, hours: 0, percentage: 0 },
        total: { minutes: 0, hours: 0 }
      }
    }
  }

  /**
   * 计算收入来源分析
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 收入来源分析
   */
  calculateIncomeSourceStatistics(workId = null, dateRange = null) {
    try {
      const cacheKey = `incomeSource_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      const stats = {
        work: 0,         // 正常工作收入
        overtime: 0,     // 加班收入
        extraIncome: 0,  // 额外收入
        deductions: 0    // 扣款
      }

      Object.values(filteredData).forEach(dayData => {
        // 计算时间段收入
        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type === 'work') {
              stats.work += segment.income || 0
            } else if (segment.type === 'overtime') {
              stats.overtime += segment.income || 0
            }
          })
        }

        // 计算收入调整
        stats.extraIncome += dayData.extraIncome || 0
        stats.deductions += dayData.deductions || 0
      })

      // 修复精度
      stats.work = Math.round(stats.work * 100) / 100
      stats.overtime = Math.round(stats.overtime * 100) / 100
      stats.extraIncome = Math.round(stats.extraIncome * 100) / 100
      stats.deductions = Math.round(stats.deductions * 100) / 100

      const totalIncome = stats.work + stats.overtime + stats.extraIncome - stats.deductions
      const baseIncome = stats.work + stats.overtime

      const result = {
        work: {
          amount: stats.work,
          percentage: totalIncome > 0 ? Math.round((stats.work / totalIncome) * 10000) / 100 : 0
        },
        overtime: {
          amount: stats.overtime,
          percentage: totalIncome > 0 ? Math.round((stats.overtime / totalIncome) * 10000) / 100 : 0
        },
        extraIncome: {
          amount: stats.extraIncome,
          percentage: totalIncome > 0 ? Math.round((stats.extraIncome / totalIncome) * 10000) / 100 : 0
        },
        deductions: {
          amount: stats.deductions,
          percentage: totalIncome > 0 ? Math.round((stats.deductions / totalIncome) * 10000) / 100 : 0
        },
        baseIncome: baseIncome,
        total: totalIncome
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算收入来源分析失败:', error)
      return {
        work: { amount: 0, percentage: 0 },
        overtime: { amount: 0, percentage: 0 },
        extraIncome: { amount: 0, percentage: 0 },
        deductions: { amount: 0, percentage: 0 },
        baseIncome: 0,
        total: 0
      }
    }
  }

  /**
   * 计算摸鱼统计
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 摸鱼统计
   */
  calculateFishingStatistics(workId = null, dateRange = null) {
    try {
      const cacheKey = `fishing_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      let totalFishingMinutes = 0
      let fishingCount = 0
      let fishingDays = 0

      Object.values(filteredData).forEach(dayData => {
        let dayFishingMinutes = 0
        let dayFishingCount = 0

        if (dayData.fishes) {
          dayData.fishes.forEach(fish => {
            const duration = fish.end - fish.start
            dayFishingMinutes += duration
            dayFishingCount++
          })
        }

        if (dayFishingCount > 0) {
          totalFishingMinutes += dayFishingMinutes
          fishingCount += dayFishingCount
          fishingDays++
        }
      })

      const result = {
        totalFishingHours: Math.round((totalFishingMinutes / 60) * 100) / 100,
        totalFishingMinutes: totalFishingMinutes,
        fishingCount: fishingCount,
        fishingDays: fishingDays,
        averageFishingPerDay: fishingDays > 0 ? Math.round((fishingCount / fishingDays) * 100) / 100 : 0,
        averageFishingDuration: fishingCount > 0 ? Math.round((totalFishingMinutes / fishingCount) * 100) / 100 : 0
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算摸鱼统计失败:', error)
      return {
        totalFishingHours: 0,
        totalFishingMinutes: 0,
        fishingCount: 0,
        fishingDays: 0,
        averageFishingPerDay: 0,
        averageFishingDuration: 0
      }
    }
  }

  /**
   * 计算工作履历统计
   * @returns {Object} 工作履历统计
   */
  calculateWorkHistoryStatistics() {
    try {
      const cacheKey = 'workHistory_all'
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const workHistoryMap = this.dataManager.getWorkHistory()
      const workList = Object.values(workHistoryMap)

      let totalWorkDays = 0
      let currentWorks = 0
      let pastWorks = 0
      let totalSalaryIncrease = 0
      let salaryIncreaseCount = 0

      // 按入职时间排序
      const sortedWorks = workList.sort((a, b) => new Date(a.startDate) - new Date(b.startDate))

      sortedWorks.forEach((work, index) => {
        // 计算工作天数
        const startDate = new Date(work.startDate)
        const endDate = work.endDate ? new Date(work.endDate) : new Date()
        const workDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
        totalWorkDays += workDays

        // 统计在职和离职数量
        if (work.endDate) {
          pastWorks++
        } else {
          currentWorks++
        }

        // 计算薪资增长（与前一份工作比较）
        if (index > 0) {
          const prevWork = sortedWorks[index - 1]
          const currentSalary = work.formalSalary || work.probationSalary || 0
          const prevSalary = prevWork.formalSalary || prevWork.probationSalary || 0

          if (prevSalary > 0 && currentSalary > 0) {
            const increase = currentSalary - prevSalary
            totalSalaryIncrease += increase
            salaryIncreaseCount++
          }
        }
      })

      const result = {
        totalWorks: workList.length,
        currentWorks: currentWorks,
        pastWorks: pastWorks,
        totalWorkDays: totalWorkDays,
        averageWorkDays: workList.length > 0 ? Math.round(totalWorkDays / workList.length) : 0,
        averageSalaryIncrease: salaryIncreaseCount > 0 ? Math.round((totalSalaryIncrease / salaryIncreaseCount) * 100) / 100 : 0,
        salaryIncreaseCount: salaryIncreaseCount
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算工作履历统计失败:', error)
      return {
        totalWorks: 0,
        currentWorks: 0,
        pastWorks: 0,
        totalWorkDays: 0,
        averageWorkDays: 0,
        averageSalaryIncrease: 0,
        salaryIncreaseCount: 0
      }
    }
  }

  /**
   * 计算时薪统计
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 时薪统计
   */
  calculateHourlyRateStatistics(workId = null, dateRange = null) {
    try {
      const cacheKey = `hourlyRate_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      const hourlyRates = []

      Object.values(filteredData).forEach(dayData => {
        // 使用动态计算方法
        const dayStats = this.dataManager.timeTrackingManager.getDayDataStats(dayData)
        const totalWorkMinutes = dayStats.totalWorkMinutes
        const netIncome = dayStats.netIncome

        // 如果有工作时间，计算基于净收入的时薪
        if (totalWorkMinutes > 0 && netIncome > 0) {
          const hours = totalWorkMinutes / 60
          const hourlyRate = netIncome / hours
          hourlyRates.push(hourlyRate)
        }
      })

      let result = {
        count: hourlyRates.length,
        average: 0,
        min: 0,
        max: 0,
        median: 0
      }

      if (hourlyRates.length > 0) {
        const sortedRates = hourlyRates.sort((a, b) => a - b)
        const sum = hourlyRates.reduce((total, rate) => total + rate, 0)

        result = {
          count: hourlyRates.length,
          average: Math.round((sum / hourlyRates.length) * 100) / 100,
          min: Math.round(sortedRates[0] * 100) / 100,
          max: Math.round(sortedRates[sortedRates.length - 1] * 100) / 100,
          median: Math.round(sortedRates[Math.floor(sortedRates.length / 2)] * 100) / 100
        }
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算时薪统计失败:', error)
      return {
        count: 0,
        average: 0,
        min: 0,
        max: 0,
        median: 0
      }
    }
  }

  /**
   * 计算日历热力图数据（类似GitHub贡献图）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 日历热力图数据
   */
  calculateCalendarHeatmapData(workId = null, dateRange = null) {
    try {
      const cacheKey = `calendarHeatmap_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      // 确定日期范围
      let startDate, endDate
      if (dateRange && dateRange.length === 2) {
        startDate = new Date(dateRange[0])
        endDate = new Date(dateRange[1])
      } else {
        // 如果没有指定范围，使用数据的日期范围
        const dataKeys = Object.keys(filteredData)
        if (dataKeys.length === 0) {
          // 如果没有数据，使用当前月
          const now = new Date()
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
        } else {
          const dates = dataKeys.map(key => new Date(key)).sort((a, b) => a - b)
          startDate = dates[0]
          endDate = dates[dates.length - 1]
        }
      }

      // 调整开始日期到周日
      const startDayOfWeek = startDate.getDay()
      const adjustedStartDate = new Date(startDate)
      adjustedStartDate.setDate(startDate.getDate() - startDayOfWeek)

      // 调整结束日期到周六
      const endDayOfWeek = endDate.getDay()
      const adjustedEndDate = new Date(endDate)
      adjustedEndDate.setDate(endDate.getDate() + (6 - endDayOfWeek))

      // 生成日历数据
      const calendarData = []
      const dailyStats = {}

      // 计算每日统计
      Object.entries(filteredData).forEach(([dateKey, dayData]) => {
        let workMinutes = 0
        let income = 0
        let fishingMinutes = 0

        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest') {
              workMinutes += segment.end - segment.start
            }
          })
        }

        // 使用动态计算的净收入
        const dayStats = this.dataManager.timeTrackingManager.getDayDataStats(dayData)
        income = dayStats.netIncome

        if (dayData.fishes) {
          dayData.fishes.forEach(fish => {
            fishingMinutes += fish.end - fish.start
          })
        }

        dailyStats[dateKey] = {
          workMinutes,
          income,
          fishingMinutes,
          workHours: Math.round((workMinutes / 60) * 100) / 100,
          hourlyRate: workMinutes > 0 ? Math.round((income / (workMinutes / 60)) * 100) / 100 : 0
        }
      })

      // 计算最大值用于强度计算
      const maxValues = {
        workMinutes: Math.max(...Object.values(dailyStats).map(d => d.workMinutes), 1),
        income: Math.max(...Object.values(dailyStats).map(d => d.income), 1),
        fishingMinutes: Math.max(...Object.values(dailyStats).map(d => d.fishingMinutes), 1),
        hourlyRate: Math.max(...Object.values(dailyStats).map(d => d.hourlyRate), 1)
      }

      // 生成日历网格
      const currentDate = new Date(adjustedStartDate)
      const weeks = []
      let currentWeek = []

      while (currentDate <= adjustedEndDate) {
        const dateKey = formatDateKey(currentDate)
        const dayStats = dailyStats[dateKey]
        const isInRange = currentDate >= startDate && currentDate <= endDate

        const dayData = {
          date: new Date(currentDate),
          dateKey: dateKey,
          day: currentDate.getDate(),
          month: currentDate.getMonth() + 1,
          year: currentDate.getFullYear(),
          dayOfWeek: currentDate.getDay(),
          isInRange: isInRange,
          hasData: !!dayStats,
          workMinutes: dayStats ? dayStats.workMinutes : 0,
          income: dayStats ? dayStats.income : 0,
          fishingMinutes: dayStats ? dayStats.fishingMinutes : 0,
          hourlyRate: dayStats ? dayStats.hourlyRate : 0,
          workHours: dayStats ? dayStats.workHours : 0
        }

        // 计算不同类型的强度
        dayData.intensities = {
          workTime: isInRange && dayStats ? Math.round((dayStats.workMinutes / maxValues.workMinutes) * 100) : 0,
          income: isInRange && dayStats ? Math.round((dayStats.income / maxValues.income) * 100) : 0,
          fishTime: isInRange && dayStats ? Math.round((dayStats.fishingMinutes / maxValues.fishingMinutes) * 100) : 0,
          hourlyRate: isInRange && dayStats ? Math.round((dayStats.hourlyRate / maxValues.hourlyRate) * 100) : 0
        }

        currentWeek.push(dayData)

        // 如果是周六或者是最后一天，结束当前周
        if (currentDate.getDay() === 6 || currentDate.getTime() === adjustedEndDate.getTime()) {
          weeks.push([...currentWeek])
          currentWeek = []
        }

        currentDate.setDate(currentDate.getDate() + 1)
      }

      const result = {
        weeks: weeks,
        calendarData: weeks.flat(),
        dateRange: {
          start: new Date(startDate),
          end: new Date(endDate),
          adjustedStart: new Date(adjustedStartDate),
          adjustedEnd: new Date(adjustedEndDate)
        },
        maxValues: maxValues,
        totalDays: weeks.flat().filter(d => d.isInRange).length,
        workDays: weeks.flat().filter(d => d.hasData).length
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算日历热力图失败:', error)
      return {
        weeks: [],
        calendarData: [],
        dateRange: {
          start: new Date(),
          end: new Date()
        },
        maxValues: { workMinutes: 1, income: 1, fishingMinutes: 1, hourlyRate: 1 },
        totalDays: 0,
        workDays: 0
      }
    }
  }

  /**
   * 计算趋势分析数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 趋势分析数据
   */
  calculateTrendAnalysis(workId = null, dateRange = null) {
    try {
      const cacheKey = `trendAnalysis_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)
      const dailyStats = []

      // 按日期排序并计算每日统计
      const sortedDates = Object.keys(filteredData).sort()

      sortedDates.forEach(dateKey => {
        const dayData = filteredData[dateKey]
        let workMinutes = 0
        let income = 0
        let segments = 0

        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest') {
              workMinutes += segment.end - segment.start
              income += segment.income || 0
              segments++
            }
          })
        }

        dailyStats.push({
          date: dateKey,
          workMinutes,
          workHours: Math.round((workMinutes / 60) * 100) / 100,
          income: Math.round(income * 100) / 100,
          segments,
          efficiency: workMinutes > 0 ? Math.round((income / (workMinutes / 60)) * 100) / 100 : 0
        })
      })

      // 计算趋势
      const calculateTrend = (data, key) => {
        if (data.length < 2) return 0
        const recent = data.slice(-7) // 最近7天
        const earlier = data.slice(-14, -7) // 之前7天

        const recentAvg = recent.reduce((sum, item) => sum + item[key], 0) / recent.length
        const earlierAvg = earlier.length > 0 ? earlier.reduce((sum, item) => sum + item[key], 0) / earlier.length : recentAvg

        return earlierAvg > 0 ? Math.round(((recentAvg - earlierAvg) / earlierAvg) * 100) : 0
      }

      const result = {
        dailyStats: dailyStats.slice(-30), // 最近30天
        trends: {
          workTime: calculateTrend(dailyStats, 'workMinutes'),
          income: calculateTrend(dailyStats, 'income'),
          efficiency: calculateTrend(dailyStats, 'efficiency')
        },
        averages: {
          workHours: dailyStats.length > 0 ? Math.round((dailyStats.reduce((sum, item) => sum + item.workMinutes, 0) / dailyStats.length / 60) * 100) / 100 : 0,
          income: dailyStats.length > 0 ? Math.round((dailyStats.reduce((sum, item) => sum + item.income, 0) / dailyStats.length) * 100) / 100 : 0,
          efficiency: dailyStats.length > 0 ? Math.round((dailyStats.reduce((sum, item) => sum + item.efficiency, 0) / dailyStats.length) * 100) / 100 : 0
        }
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算趋势分析失败:', error)
      return {
        dailyStats: [],
        trends: { workTime: 0, income: 0, efficiency: 0 },
        averages: { workHours: 0, income: 0, efficiency: 0 }
      }
    }
  }

  /**
   * 计算对比分析数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 对比分析数据
   */
  calculateComparisonAnalysis(workId = null, dateRange = null) {
    try {
      const cacheKey = `comparisonAnalysis_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const now = new Date()

      // 计算当前周期和上个周期的数据
      const currentPeriod = this.calculateOverviewStatistics(workId, dateRange)

      // 计算上个周期的日期范围
      let previousDateRange = null
      if (dateRange && dateRange.length === 2) {
        const start = new Date(dateRange[0])
        const end = new Date(dateRange[1])
        const duration = end.getTime() - start.getTime()

        const previousEnd = new Date(start.getTime() - 24 * 60 * 60 * 1000) // 前一天
        const previousStart = new Date(previousEnd.getTime() - duration)

        previousDateRange = [previousStart, previousEnd]
      }

      const previousPeriod = this.calculateOverviewStatistics(workId, previousDateRange)

      // 计算变化率
      const calculateChange = (current, previous) => {
        if (previous === 0) return current > 0 ? 100 : 0
        return Math.round(((current - previous) / previous) * 100)
      }

      const result = {
        current: currentPeriod,
        previous: previousPeriod,
        changes: {
          workHours: calculateChange(currentPeriod.totalWorkHours, previousPeriod.totalWorkHours),
          income: calculateChange(currentPeriod.totalIncome, previousPeriod.totalIncome),
          workDays: calculateChange(currentPeriod.workDays, previousPeriod.workDays),
          hourlyRate: calculateChange(currentPeriod.averageHourlyRate, previousPeriod.averageHourlyRate)
        }
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算对比分析失败:', error)
      return {
        current: {},
        previous: {},
        changes: { workHours: 0, income: 0, workDays: 0, hourlyRate: 0 }
      }
    }
  }

  /**
   * 计算排行榜数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 排行榜数据
   */
  calculateRankingAnalysis(workId = null, dateRange = null) {
    try {
      const cacheKey = `rankingAnalysis_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)
      const dailyRecords = []

      // 计算每日记录
      Object.entries(filteredData).forEach(([dateKey, dayData]) => {
        let workMinutes = 0
        let income = 0
        let segments = 0
        let fishingMinutes = 0

        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest') {
              workMinutes += segment.end - segment.start
              income += segment.income || 0
              segments++
            }
          })
        }

        if (dayData.fishes) {
          dayData.fishes.forEach(fish => {
            fishingMinutes += fish.end - fish.start
          })
        }

        if (workMinutes > 0 || income > 0) {
          const date = new Date(dateKey)
          dailyRecords.push({
            date: dateKey,
            dateText: `${date.getMonth() + 1}月${date.getDate()}日`,
            workMinutes,
            workHours: Math.round((workMinutes / 60) * 100) / 100,
            income: Math.round(income * 100) / 100,
            segments,
            fishingMinutes,
            efficiency: workMinutes > 0 ? Math.round((income / (workMinutes / 60)) * 100) / 100 : 0
          })
        }
      })

      // 生成排行榜
      const topIncome = [...dailyRecords]
        .sort((a, b) => b.income - a.income)
        .slice(0, 5)
        .map((record, index) => ({ ...record, rank: index + 1 }))

      const topWorkHours = [...dailyRecords]
        .sort((a, b) => b.workMinutes - a.workMinutes)
        .slice(0, 5)
        .map((record, index) => ({ ...record, rank: index + 1 }))

      const topEfficiency = [...dailyRecords]
        .filter(record => record.efficiency > 0)
        .sort((a, b) => b.efficiency - a.efficiency)
        .slice(0, 5)
        .map((record, index) => ({ ...record, rank: index + 1 }))

      const topFishing = [...dailyRecords]
        .filter(record => record.fishingMinutes > 0)
        .sort((a, b) => b.fishingMinutes - a.fishingMinutes)
        .slice(0, 5)
        .map((record, index) => ({ ...record, rank: index + 1 }))

      const result = {
        topIncome,
        topWorkHours,
        topEfficiency,
        topFishing,
        totalRecords: dailyRecords.length
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算排行榜失败:', error)
      return {
        topIncome: [],
        topWorkHours: [],
        topEfficiency: [],
        topFishing: [],
        totalRecords: 0
      }
    }
  }



  /**
   * 计算实时统计数据（今日、本周等）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 实时统计数据
   */
  calculateRealTimeStats(workId = null) {
    try {
      const now = new Date()
      const today = [getDateStart(now), getDateStart(now)]
      const thisWeek = [getWeekStart(now), getDateStart(now)]

      // 今日统计
      const todayStats = this.calculateOverviewStatistics(workId, today)

      // 本周统计
      const weekStats = this.calculateOverviewStatistics(workId, thisWeek)

      // 计算连续工作天数
      const allData = this.getFilteredDailyData(workId, null)
      const sortedDates = Object.keys(allData).sort().reverse()

      let currentStreak = 0
      let maxStreak = 0
      let tempStreak = 0

      const today_key = formatDateKey(now)

      for (let i = 0; i < sortedDates.length; i++) {
        const dateKey = sortedDates[i]
        const dayData = allData[dateKey]

        let hasWork = false
        if (dayData.segments) {
          hasWork = dayData.segments.some(segment => segment.type !== 'rest')
        }

        if (hasWork) {
          tempStreak++
          if (i === 0 || dateKey === today_key) {
            currentStreak = tempStreak
          }
          maxStreak = Math.max(maxStreak, tempStreak)
        } else {
          tempStreak = 0
        }
      }

      return {
        today: {
          ...todayStats,
          isWorkDay: todayStats.workDays > 0,
          progress: Math.min((todayStats.totalWorkHours / 8) * 100, 100) // 假设8小时为满工作日
        },
        thisWeek: {
          ...weekStats,
          targetDays: 5, // 工作日目标
          completionRate: Math.min((weekStats.workDays / 5) * 100, 100)
        },
        streaks: {
          current: currentStreak,
          max: maxStreak,
          isActive: currentStreak > 0
        }
      }
    } catch (error) {
      console.error('计算实时统计失败:', error)
      return {
        today: { totalWorkHours: 0, totalIncome: 0, isWorkDay: false, progress: 0 },
        thisWeek: { workDays: 0, totalWorkHours: 0, totalIncome: 0, targetDays: 5, completionRate: 0 },
        streaks: { current: 0, max: 0, isActive: false }
      }
    }
  }

  /**
   * 计算历史总览数据（全局统计）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 历史总览数据
   */
  calculateHistoricalOverview(workId = null) {
    try {
      const cacheKey = `historicalOverview_${workId || 'current'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      // 获取所有数据（不限制日期范围）
      const allData = this.getFilteredDailyData(workId, null)
      const allStats = this.calculateOverviewStatistics(workId, null)

      // 计算历史最佳记录
      const dailyRecords = []
      Object.entries(allData).forEach(([dateKey, dayData]) => {
        let workMinutes = 0
        let income = 0

        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest') {
              workMinutes += segment.end - segment.start
              income += segment.income || 0
            }
          })
        }

        if (workMinutes > 0) {
          dailyRecords.push({
            date: dateKey,
            workMinutes,
            income: Math.round(income * 100) / 100,
            efficiency: workMinutes > 0 ? Math.round((income / (workMinutes / 60)) * 100) / 100 : 0
          })
        }
      })

      // 历史最佳
      const bestDay = {
        income: dailyRecords.reduce((best, current) => current.income > best.income ? current : best, { income: 0, date: '' }),
        workTime: dailyRecords.reduce((best, current) => current.workMinutes > best.workMinutes ? current : best, { workMinutes: 0, date: '' }),
        efficiency: dailyRecords.reduce((best, current) => current.efficiency > best.efficiency ? current : best, { efficiency: 0, date: '' })
      }

      // 计算工作天数分布
      const workDaysPerMonth = {}
      dailyRecords.forEach(record => {
        const date = new Date(record.date)
        const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`
        workDaysPerMonth[monthKey] = (workDaysPerMonth[monthKey] || 0) + 1
      })

      const result = {
        allTime: allStats,
        bestRecords: {
          highestIncome: {
            amount: bestDay.income.income,
            date: bestDay.income.date,
            dateText: bestDay.income.date || ''
          },
          longestWork: {
            minutes: bestDay.workTime.workMinutes,
            hours: Math.round((bestDay.workTime.workMinutes / 60) * 100) / 100,
            date: bestDay.workTime.date,
            dateText: bestDay.workTime.date || ''
          },
          bestEfficiency: {
            rate: bestDay.efficiency.efficiency,
            date: bestDay.efficiency.date,
            dateText: bestDay.efficiency.date || ''
          }
        },
        milestones: {
          totalDays: dailyRecords.length,
          totalMonths: Object.keys(workDaysPerMonth).length,
          avgDaysPerMonth: Object.keys(workDaysPerMonth).length > 0 ?
            Math.round(Object.values(workDaysPerMonth).reduce((sum, days) => sum + days, 0) / Object.keys(workDaysPerMonth).length) : 0,
          firstWorkDate: dailyRecords.length > 0 ? dailyRecords.sort((a, b) => new Date(a.date) - new Date(b.date))[0].date : null
        }
      }

      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('计算历史总览失败:', error)
      return {
        allTime: {},
        bestRecords: {},
        milestones: {}
      }
    }
  }

  /**
   * 计算成就统计
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 成就统计数据
   */
  calculateAchievements(workId = null) {
    try {
      const allStats = this.calculateOverviewStatistics(workId, null)
      const realTimeStats = this.calculateRealTimeStats(workId)

      const achievements = []

      // 工作时长成就
      if (allStats.totalWorkHours >= 1000) {
        achievements.push({ id: 'work_1000h', title: '千小时达人', desc: '累计工作1000小时', icon: '🏆', unlocked: true })
      } else if (allStats.totalWorkHours >= 500) {
        achievements.push({ id: 'work_500h', title: '五百小时', desc: '累计工作500小时', icon: '🥇', unlocked: true })
      } else if (allStats.totalWorkHours >= 100) {
        achievements.push({ id: 'work_100h', title: '百小时里程碑', desc: '累计工作100小时', icon: '🥈', unlocked: true })
      }

      // 收入成就
      if (allStats.totalIncome >= 100000) {
        achievements.push({ id: 'income_100k', title: '十万收入', desc: '累计收入10万元', icon: '💰', unlocked: true })
      } else if (allStats.totalIncome >= 50000) {
        achievements.push({ id: 'income_50k', title: '五万收入', desc: '累计收入5万元', icon: '💵', unlocked: true })
      } else if (allStats.totalIncome >= 10000) {
        achievements.push({ id: 'income_10k', title: '万元户', desc: '累计收入1万元', icon: '💸', unlocked: true })
      }

      // 连续工作成就
      if (realTimeStats.streaks.max >= 30) {
        achievements.push({ id: 'streak_30', title: '月度坚持', desc: '连续工作30天', icon: '🔥', unlocked: true })
      } else if (realTimeStats.streaks.max >= 7) {
        achievements.push({ id: 'streak_7', title: '周度坚持', desc: '连续工作7天', icon: '⚡', unlocked: true })
      }

      // 效率成就
      if (allStats.averageHourlyRate >= 200) {
        achievements.push({ id: 'efficiency_200', title: '高效达人', desc: '平均时薪超过200元', icon: '🚀', unlocked: true })
      } else if (allStats.averageHourlyRate >= 100) {
        achievements.push({ id: 'efficiency_100', title: '效率专家', desc: '平均时薪超过100元', icon: '⭐', unlocked: true })
      }

      return {
        achievements,
        totalUnlocked: achievements.filter(a => a.unlocked).length,
        totalAchievements: achievements.length + 10 // 假设总共有更多成就
      }
    } catch (error) {
      console.error('计算成就统计失败:', error)
      return {
        achievements: [],
        totalUnlocked: 0,
        totalAchievements: 0
      }
    }
  }

  /**
   * 计算工作效率分布数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 工作效率分布数据
   */
  calculateEfficiencyDistribution(workId = null, dateRange = null) {
    try {
      const filteredData = this.getFilteredDailyData(workId, dateRange)
      const efficiencyRanges = [
        { min: 0, max: 50, label: '0-50元/h', count: 0, totalHours: 0, totalIncome: 0 },
        { min: 50, max: 100, label: '50-100元/h', count: 0, totalHours: 0, totalIncome: 0 },
        { min: 100, max: 150, label: '100-150元/h', count: 0, totalHours: 0, totalIncome: 0 },
        { min: 150, max: 200, label: '150-200元/h', count: 0, totalHours: 0, totalIncome: 0 },
        { min: 200, max: Infinity, label: '200元/h以上', count: 0, totalHours: 0, totalIncome: 0 }
      ]

      Object.values(filteredData).forEach(dayData => {
        let workMinutes = 0
        let income = 0

        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest') {
              workMinutes += segment.end - segment.start
              income += segment.income || 0
            }
          })
        }

        if (workMinutes > 0) {
          const hourlyRate = income / (workMinutes / 60)
          const range = efficiencyRanges.find(r => hourlyRate >= r.min && hourlyRate < r.max)
          if (range) {
            range.count++
            range.totalHours += workMinutes / 60
            range.totalIncome += income
          }
        }
      })

      // 计算百分比
      const totalDays = efficiencyRanges.reduce((sum, range) => sum + range.count, 0)
      efficiencyRanges.forEach(range => {
        range.percentage = totalDays > 0 ? Math.round((range.count / totalDays) * 100) : 0
      })

      return {
        ranges: efficiencyRanges,
        totalDays: totalDays
      }
    } catch (error) {
      console.error('计算效率分布失败:', error)
      return { ranges: [], totalDays: 0 }
    }
  }

  /**
   * 计算收入来源详细分析
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 收入来源详细分析
   */
  calculateIncomeSourceAnalysis(workId = null, dateRange = null) {
    try {
      const filteredData = this.getFilteredDailyData(workId, dateRange)
      const sourceStats = {
        work: {
          name: '正常工作',
          totalIncome: 0,
          totalHours: 0,
          sessions: 0,
          avgHourlyRate: 0
        },
        overtime: {
          name: '加班工作',
          totalIncome: 0,
          totalHours: 0,
          sessions: 0,
          avgHourlyRate: 0
        }
      }
      let totalIncome = 0

      Object.values(filteredData).forEach(dayData => {
        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest' && segment.income > 0) {
              // 根据工作类型分类收入
              const sourceKey = segment.type === 'overtime' ? 'overtime' : 'work'
              const source = sourceStats[sourceKey]

              source.totalIncome += segment.income
              source.totalHours += (segment.end - segment.start) / 60
              source.sessions++
              totalIncome += segment.income
            }
          })
        }
      })

      // 计算平均时薪和百分比
      const sources = Object.values(sourceStats).map(source => ({
        ...source,
        avgHourlyRate: source.totalHours > 0 ? Math.round((source.totalIncome / source.totalHours) * 100) / 100 : 0,
        percentage: totalIncome > 0 ? Math.round((source.totalIncome / totalIncome) * 100) : 0
      })).filter(source => source.totalIncome > 0) // 只显示有收入的类型
        .sort((a, b) => b.totalIncome - a.totalIncome)

      return {
        sources: sources,
        totalIncome: totalIncome,
        totalSources: sources.length
      }
    } catch (error) {
      console.error('计算收入来源分析失败:', error)
      return { sources: [], totalIncome: 0, totalSources: 0 }
    }
  }



  /**
   * 计算工作节奏分析
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 工作节奏分析数据
   */
  calculateWorkRhythmAnalysis(workId = null, dateRange = null) {
    try {
      const filteredData = this.getFilteredDailyData(workId, dateRange)
      const rhythmData = {
        morningPerson: 0, // 早起型 (6-12点工作多)
        afternoonPerson: 0, // 下午型 (12-18点工作多)
        nightOwl: 0, // 夜猫子 (18-24点工作多)
        allDay: 0, // 全天型 (各时段都有)
        totalDays: 0
      }

      Object.values(filteredData).forEach(dayData => {
        if (dayData.segments) {
          let morningMinutes = 0
          let afternoonMinutes = 0
          let nightMinutes = 0

          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest') {
              const startHour = Math.floor(segment.start / 60)
              const endHour = Math.floor(segment.end / 60)
              const duration = segment.end - segment.start

              // 分时段统计
              for (let hour = startHour; hour <= endHour && hour < 24; hour++) {
                const hourStart = hour * 60
                const hourEnd = (hour + 1) * 60
                const segmentStart = Math.max(segment.start, hourStart)
                const segmentEnd = Math.min(segment.end, hourEnd)

                if (segmentEnd > segmentStart) {
                  const minutesInHour = segmentEnd - segmentStart

                  if (hour >= 6 && hour < 12) {
                    morningMinutes += minutesInHour
                  } else if (hour >= 12 && hour < 18) {
                    afternoonMinutes += minutesInHour
                  } else if (hour >= 18 && hour < 24) {
                    nightMinutes += minutesInHour
                  }
                }
              }
            }
          })

          if (morningMinutes > 0 || afternoonMinutes > 0 || nightMinutes > 0) {
            rhythmData.totalDays++

            const total = morningMinutes + afternoonMinutes + nightMinutes
            const morningRatio = morningMinutes / total
            const afternoonRatio = afternoonMinutes / total
            const nightRatio = nightMinutes / total

            // 判断工作类型
            if (morningRatio > 0.5) {
              rhythmData.morningPerson++
            } else if (afternoonRatio > 0.5) {
              rhythmData.afternoonPerson++
            } else if (nightRatio > 0.5) {
              rhythmData.nightOwl++
            } else {
              rhythmData.allDay++
            }
          }
        }
      })

      // 计算百分比
      Object.keys(rhythmData).forEach(key => {
        if (key !== 'totalDays') {
          rhythmData[key + 'Percentage'] = rhythmData.totalDays > 0 ?
            Math.round((rhythmData[key] / rhythmData.totalDays) * 100) : 0
        }
      })

      return rhythmData
    } catch (error) {
      console.error('计算工作节奏分析失败:', error)
      return {
        morningPerson: 0, afternoonPerson: 0, nightOwl: 0, allDay: 0,
        morningPersonPercentage: 0, afternoonPersonPercentage: 0,
        nightOwlPercentage: 0, allDayPercentage: 0, totalDays: 0
      }
    }
  }



  /**
   * 计算收入增长趋势
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 收入增长趋势数据
   */
  calculateIncomeGrowthTrend(workId = null, dateRange = null) {
    try {
      const filteredData = this.getFilteredDailyData(workId, dateRange)
      const monthlyData = {}

      // 按月份统计收入
      Object.entries(filteredData).forEach(([dateKey, dayData]) => {
        const date = new Date(dateKey)
        const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`

        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = {
            month: monthKey,
            monthName: `${date.getFullYear()}年${date.getMonth() + 1}月`,
            income: 0,
            workDays: 0,
            workHours: 0
          }
        }

        let dayIncome = 0
        let dayMinutes = 0

        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            if (segment.type !== 'rest') {
              dayIncome += segment.income || 0
              dayMinutes += segment.end - segment.start
            }
          })
        }

        if (dayMinutes > 0) {
          monthlyData[monthKey].income += dayIncome
          monthlyData[monthKey].workDays++
          monthlyData[monthKey].workHours += dayMinutes / 60
        }
      })

      // 转换为数组并排序
      const trendData = Object.values(monthlyData)
        .sort((a, b) => a.month.localeCompare(b.month))
        .map((month, index, array) => {
          const prevMonth = index > 0 ? array[index - 1] : null
          const growth = prevMonth ?
            Math.round(((month.income - prevMonth.income) / prevMonth.income) * 100) : 0

          return {
            ...month,
            income: Math.round(month.income * 100) / 100,
            workHours: Math.round(month.workHours * 100) / 100,
            avgDailyIncome: month.workDays > 0 ? Math.round((month.income / month.workDays) * 100) / 100 : 0,
            growth: growth,
            growthDirection: growth > 0 ? 'up' : growth < 0 ? 'down' : 'stable'
          }
        })

      return {
        trendData: trendData,
        totalMonths: trendData.length,
        totalIncome: trendData.reduce((sum, month) => sum + month.income, 0),
        avgMonthlyIncome: trendData.length > 0 ?
          Math.round((trendData.reduce((sum, month) => sum + month.income, 0) / trendData.length) * 100) / 100 : 0
      }
    } catch (error) {
      console.error('计算收入增长趋势失败:', error)
      return { trendData: [], totalMonths: 0, totalIncome: 0, avgMonthlyIncome: 0 }
    }
  }

  /**
   * 计算时间分布饼图数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 时间分布饼图数据
   */
  calculateTimeDistributionPie(workId = null, dateRange = null) {
    try {
      const filteredData = this.getFilteredDailyData(workId, dateRange)

      let workMinutes = 0
      let overtimeMinutes = 0
      let restMinutes = 0

      Object.values(filteredData).forEach(dayData => {
        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            const duration = segment.end - segment.start

            if (segment.type === 'rest') {
              restMinutes += duration
            } else {
              // 判断是否为加班（这里简化为超过8小时的部分算加班）
              const dailyWorkMinutes = dayData.segments
                .filter(s => s.type !== 'rest')
                .reduce((sum, s) => sum + (s.end - s.start), 0)

              if (dailyWorkMinutes > 8 * 60) {
                const overtimePart = Math.min(duration, dailyWorkMinutes - 8 * 60)
                overtimeMinutes += overtimePart
                workMinutes += duration - overtimePart
              } else {
                workMinutes += duration
              }
            }
          })
        }

        // 添加摸鱼时间到休息时间
        if (dayData.fishes) {
          dayData.fishes.forEach(fish => {
            restMinutes += fish.end - fish.start
          })
        }
      })

      const totalMinutes = workMinutes + overtimeMinutes + restMinutes

      if (totalMinutes === 0) {
        return {
          segments: [],
          totalMinutes: 0
        }
      }

      const segments = [
        {
          type: 'work',
          label: '正常工作',
          minutes: workMinutes,
          hours: Math.round((workMinutes / 60) * 100) / 100,
          percentage: Math.round((workMinutes / totalMinutes) * 100),
          color: '#3b82f6'
        },
        {
          type: 'overtime',
          label: '加班时间',
          minutes: overtimeMinutes,
          hours: Math.round((overtimeMinutes / 60) * 100) / 100,
          percentage: Math.round((overtimeMinutes / totalMinutes) * 100),
          color: '#ef4444'
        },
        {
          type: 'rest',
          label: '休息摸鱼',
          minutes: restMinutes,
          hours: Math.round((restMinutes / 60) * 100) / 100,
          percentage: Math.round((restMinutes / totalMinutes) * 100),
          color: '#10b981'
        }
      ].filter(segment => segment.minutes > 0)

      // 计算饼图的旋转角度
      let currentRotation = 0
      segments.forEach(segment => {
        segment.rotation = currentRotation
        currentRotation += (segment.percentage / 100) * 360
      })

      return {
        segments: segments,
        totalMinutes: totalMinutes,
        totalHours: Math.round((totalMinutes / 60) * 100) / 100
      }
    } catch (error) {
      console.error('计算时间分布饼图失败:', error)
      return {
        segments: [],
        totalMinutes: 0,
        totalHours: 0
      }
    }
  }

  /**
   * 获取综合统计数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 综合统计数据
   */
  getComprehensiveStatistics(workId = null, dateRange = null) {
    try {
      return {
        // 时间范围相关数据
        overview: this.calculateOverviewStatistics(workId, dateRange),
        timeType: this.calculateTimeTypeStatistics(workId, dateRange),
        incomeSource: this.calculateIncomeSourceStatistics(workId, dateRange),
        fishing: this.calculateFishingStatistics(workId, dateRange),
        hourlyRate: this.calculateHourlyRateStatistics(workId, dateRange),
        calendarHeatmap: this.calculateCalendarHeatmapData(workId, dateRange),
        comparisonAnalysis: this.calculateComparisonAnalysis(workId, dateRange),
        rankingAnalysis: this.calculateRankingAnalysis(workId, dateRange),

        // 数据可视化
        efficiencyDistribution: this.calculateEfficiencyDistribution(workId, dateRange),
        incomeSourceAnalysis: this.calculateIncomeSourceAnalysis(workId, dateRange),
        workRhythmAnalysis: this.calculateWorkRhythmAnalysis(workId, dateRange),
        incomeGrowthTrend: this.calculateIncomeGrowthTrend(workId, dateRange),
        timeDistributionPie: this.calculateTimeDistributionPie(workId, dateRange),

        // 全局数据（不受时间范围影响）
        historical: this.calculateHistoricalOverview(workId),
        workHistory: this.calculateWorkHistoryStatistics(),

        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      console.error('获取综合统计数据失败:', error)
      return {
        overview: {},
        timeType: {},
        incomeSource: {},
        fishing: {},
        hourlyRate: {},
        calendarHeatmap: {},
        comparisonAnalysis: {},
        rankingAnalysis: {},
        efficiencyDistribution: {},
        incomeSourceAnalysis: {},
        workRhythmAnalysis: {},
        incomeGrowthTrend: {},
        timeDistributionPie: {},
        historical: {},
        workHistory: {},
        generatedAt: new Date().toISOString()
      }
    }
  }

  // ==================== 收入调整统计方法 ====================

  /**
   * 计算收入调整统计
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 收入调整统计结果
   */
  calculateIncomeAdjustmentStatistics(workId = null, dateRange = null) {
    try {
      const cacheKey = `incomeAdjustment_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      const stats = {
        totalExtraIncome: 0,
        totalDeductions: 0,
        netAdjustment: 0,
        adjustmentDays: 0,
        extraIncomeByType: {},
        deductionsByType: {},
        monthlyTrend: {},
        averageAdjustment: 0
      }

      // 遍历所有日期数据
      Object.entries(filteredData).forEach(([dateKey, dayData]) => {
        let dayExtraIncome = 0
        let dayDeductions = 0
        let hasAdjustment = false

        // 统计额外收入
        if (dayData.extraIncomeItems && Array.isArray(dayData.extraIncomeItems)) {
          dayData.extraIncomeItems.forEach(item => {
            dayExtraIncome += item.amount || 0
            hasAdjustment = true

            // 按类型统计
            if (!stats.extraIncomeByType[item.type]) {
              stats.extraIncomeByType[item.type] = {
                amount: 0,
                count: 0,
                typeName: item.type || '未设置类型'  // 直接使用用户输入的类型
              }
            }
            stats.extraIncomeByType[item.type].amount += item.amount || 0
            stats.extraIncomeByType[item.type].count++
          })
        }

        // 统计扣款
        if (dayData.deductionItems && Array.isArray(dayData.deductionItems)) {
          dayData.deductionItems.forEach(item => {
            dayDeductions += item.amount || 0
            hasAdjustment = true

            // 按类型统计
            if (!stats.deductionsByType[item.type]) {
              stats.deductionsByType[item.type] = {
                amount: 0,
                count: 0,
                typeName: item.type || '未设置类型'  // 直接使用用户输入的类型
              }
            }
            stats.deductionsByType[item.type].amount += item.amount || 0
            stats.deductionsByType[item.type].count++
          })
        }

        // 累计统计
        stats.totalExtraIncome += dayExtraIncome
        stats.totalDeductions += dayDeductions

        if (hasAdjustment) {
          stats.adjustmentDays++
        }

        // 按月统计
        const date = new Date(dateKey)
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

        if (!stats.monthlyTrend[monthKey]) {
          stats.monthlyTrend[monthKey] = {
            month: monthKey,
            monthName: `${date.getFullYear()}年${date.getMonth() + 1}月`,
            extraIncome: 0,
            deductions: 0,
            netAdjustment: 0,
            adjustmentDays: 0
          }
        }

        stats.monthlyTrend[monthKey].extraIncome += dayExtraIncome
        stats.monthlyTrend[monthKey].deductions += dayDeductions
        stats.monthlyTrend[monthKey].netAdjustment += (dayExtraIncome - dayDeductions)

        if (hasAdjustment) {
          stats.monthlyTrend[monthKey].adjustmentDays++
        }
      })

      // 计算净调整和平均值
      stats.netAdjustment = stats.totalExtraIncome - stats.totalDeductions
      stats.averageAdjustment = stats.adjustmentDays > 0 ?
        Math.round((stats.netAdjustment / stats.adjustmentDays) * 100) / 100 : 0

      // 格式化数值
      stats.totalExtraIncome = Math.round(stats.totalExtraIncome * 100) / 100
      stats.totalDeductions = Math.round(stats.totalDeductions * 100) / 100
      stats.netAdjustment = Math.round(stats.netAdjustment * 100) / 100

      // 转换月度趋势为数组
      stats.monthlyTrendArray = Object.values(stats.monthlyTrend)
        .sort((a, b) => a.month.localeCompare(b.month))
        .map(month => ({
          ...month,
          extraIncome: Math.round(month.extraIncome * 100) / 100,
          deductions: Math.round(month.deductions * 100) / 100,
          netAdjustment: Math.round(month.netAdjustment * 100) / 100
        }))

      this.setCache(cacheKey, stats)
      return stats

    } catch (error) {
      console.error('计算收入调整统计失败:', error)
      return {
        totalExtraIncome: 0,
        totalDeductions: 0,
        netAdjustment: 0,
        adjustmentDays: 0,
        extraIncomeByType: {},
        deductionsByType: {},
        monthlyTrend: {},
        monthlyTrendArray: [],
        averageAdjustment: 0
      }
    }
  }

  /**
   * 计算扩展的收入来源分析（包含收入调整）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Array} dateRange - 日期范围，可选
   * @returns {Object} 扩展收入来源分析
   */
  calculateExtendedIncomeSourceAnalysis(workId = null, dateRange = null) {
    try {
      const cacheKey = `extendedIncomeSource_${workId || 'current'}_${dateRange ? dateRange.join('_') : 'all'}`
      const cached = this.getFromCache(cacheKey)
      if (cached) return cached

      const filteredData = this.getFilteredDailyData(workId, dateRange)

      const sourceStats = {
        baseIncome: 0,      // 基础收入（时间段收入）
        extraIncome: 0,     // 额外收入
        deductions: 0,      // 扣款
        netIncome: 0        // 净收入
      }

      // 遍历所有日期数据
      Object.values(filteredData).forEach(dayData => {
        // 使用动态计算方法
        const dayStats = this.dataManager.timeTrackingManager.getDayDataStats(dayData)

        // 基础收入（时间段收入）
        sourceStats.baseIncome += dayStats.dailyIncome

        // 额外收入
        sourceStats.extraIncome += dayStats.extraIncome

        // 扣款
        sourceStats.deductions += dayStats.deductions
      })

      // 计算净收入
      sourceStats.netIncome = sourceStats.baseIncome + sourceStats.extraIncome - sourceStats.deductions

      // 计算百分比
      const totalPositiveIncome = sourceStats.baseIncome + sourceStats.extraIncome

      const result = {
        baseIncome: {
          amount: Math.round(sourceStats.baseIncome * 100) / 100,
          percentage: totalPositiveIncome > 0 ?
            Math.round((sourceStats.baseIncome / totalPositiveIncome) * 100) : 0,
          label: '基础收入'
        },
        extraIncome: {
          amount: Math.round(sourceStats.extraIncome * 100) / 100,
          percentage: totalPositiveIncome > 0 ?
            Math.round((sourceStats.extraIncome / totalPositiveIncome) * 100) : 0,
          label: '额外收入'
        },
        deductions: {
          amount: Math.round(sourceStats.deductions * 100) / 100,
          percentage: sourceStats.netIncome > 0 ?
            Math.round((sourceStats.deductions / sourceStats.netIncome) * 100) : 0,
          label: '扣款'
        },
        netIncome: {
          amount: Math.round(sourceStats.netIncome * 100) / 100,
          label: '净收入'
        },
        totalPositiveIncome: Math.round(totalPositiveIncome * 100) / 100
      }

      this.setCache(cacheKey, result)
      return result

    } catch (error) {
      console.error('计算扩展收入来源分析失败:', error)
      return {
        baseIncome: { amount: 0, percentage: 0, label: '基础收入' },
        extraIncome: { amount: 0, percentage: 0, label: '额外收入' },
        deductions: { amount: 0, percentage: 0, label: '扣款' },
        netIncome: { amount: 0, label: '净收入' },
        totalPositiveIncome: 0
      }
    }
  }

  /**
   * 获取收入类型文本（现在直接返回用户输入的类型）
   * @param {string} type - 用户输入的收入类型
   * @returns {string} 类型文本
   */
  getIncomeTypeText(type) {
    return type || '未设置类型'
  }

  /**
   * 获取扣款类型文本（现在直接返回用户输入的类型）
   * @param {string} type - 用户输入的扣款类型
   * @returns {string} 类型文本
   */
  getDeductionTypeText(type) {
    return type || '未设置类型'
  }
}

// 导出单例实例
let statisticsServiceInstance = null

function getStatisticsService() {
  if (!statisticsServiceInstance) {
    statisticsServiceInstance = new StatisticsService()
  }
  return statisticsServiceInstance
}

module.exports = {
  StatisticsService,
  getStatisticsService
}
