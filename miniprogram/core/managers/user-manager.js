/**
 * 用户管理器
 * 负责用户信息的获取、缓存和管理
 *
 * 功能特性：
 * - 用户信息缓存
 * - 自动登录和用户创建
 * - 用户状态管理
 * - 会员信息管理
 */

import { api } from '../api/index.js'

class UserManager {
  constructor() {
    // 单例模式
    if (UserManager.instance) {
      return UserManager.instance
    }

    // 用户信息缓存
    this.userInfo = null
    this.isLoggedIn = false
    this.isLoading = false
    this.loadPromise = null

    // 缓存键名
    this.storageKey = 'USER_INFO_CACHE'
    this.cacheTimeout = -1 // Todo: 暂时禁用缓存

    // 变化监听器
    this.changeListeners = []

    UserManager.instance = this
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!UserManager.instance) {
      UserManager.instance = new UserManager()
    }
    return UserManager.instance
  }

  /**
   * 初始化用户信息
   * 应用启动时调用
   */
  async initialize() {
    if (this.isLoading) {
      return this.loadPromise
    }

    this.isLoading = true
    this.loadPromise = this._doInitialize()

    try {
      await this.loadPromise
    } finally {
      this.isLoading = false
      this.loadPromise = null
    }
  }

  /**
   * 强制重新获取用户信息
   */
  async refresh() {
    console.log('强制刷新用户信息...')
    this.userInfo = null
    this.isLoggedIn = false
    await this.initialize()
  }

  /**
   * 执行初始化逻辑
   */
  async _doInitialize() {
    try {
      console.log('开始初始化用户信息...')

      // 如果已经有用户信息且未过期，直接返回
      if (this.userInfo && this.isLoggedIn && this._isCacheValid()) {
        console.log('用户信息已存在且有效，跳过初始化')
        return
      }

      // 先尝试从本地缓存加载
      this._loadFromCache()

      // 检查缓存是否有效
      if (this._isCacheValid()) {
        console.log('使用缓存的用户信息')
        this.isLoggedIn = true
        this.notifyChange()
        return
      }

      // 缓存无效，从云端获取
      await this._loadFromCloud()

    } catch (error) {
      console.error('初始化用户信息失败:', error)
      // 初始化失败时清除缓存
      this._clearCache()
    }
  }

  /**
   * 从云端加载用户信息
   */
  async _loadFromCloud() {
    try {
      console.log('从云端获取用户信息...')

      // 获取用户信息
      const result = await api.user.getUserInfo({
        showLoading: false,
        showError: false
      })

      if (result.success) {
        this.userInfo = result.data
        this.isLoggedIn = true

        // 保存到本地缓存
        this._saveToCache()

        console.log('用户信息获取成功:', this.userInfo.no)
        this.notifyChange()

        // 通知用户登录完成，触发同步检查
        this.notifyLoginComplete()
      } else {
        throw new Error(result.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('从云端加载用户信息失败:', error)
      throw error
    }
  }

  /**
   * 从本地缓存加载用户信息
   */
  _loadFromCache() {
    try {
      const cacheData = wx.getStorageSync(this.storageKey)
      if (cacheData && cacheData.userInfo && cacheData.timestamp) {
        this.userInfo = cacheData.userInfo
        console.log('从缓存加载用户信息:', this.userInfo.no)
      }
    } catch (error) {
      console.error('从缓存加载用户信息失败:', error)
    }
  }

  /**
   * 保存用户信息到本地缓存
   */
  _saveToCache() {
    try {
      const cacheData = {
        userInfo: this.userInfo,
        timestamp: Date.now()
      }
      wx.setStorageSync(this.storageKey, cacheData)
    } catch (error) {
      console.error('保存用户信息到缓存失败:', error)
    }
  }

  /**
   * 检查缓存是否有效
   */
  _isCacheValid() {
    if (!this.userInfo) {
      return false
    }

    try {
      const cacheData = wx.getStorageSync(this.storageKey)
      if (!cacheData || !cacheData.timestamp) {
        return false
      }

      const now = Date.now()
      const cacheAge = now - cacheData.timestamp
      return cacheAge < this.cacheTimeout
    } catch (error) {
      return false
    }
  }

  /**
   * 清除缓存
   */
  _clearCache() {
    try {
      wx.removeStorageSync(this.storageKey)
      this.userInfo = null
      this.isLoggedIn = false
    } catch (error) {
      console.error('清除用户缓存失败:', error)
    }
  }

  /**
   * 获取用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    return this.userInfo
  }

  /**
   * 获取用户编号
   * @returns {number|null} 用户编号
   */
  getUserNo() {
    return this.userInfo ? this.userInfo.no : null
  }

  /**
   * 获取用户昵称
   * @returns {string} 用户昵称
   */
  getNickname() {
    return this.userInfo ? this.userInfo.nickname : '未登录'
  }

  /**
   * 获取用户头像
   * @returns {string} 用户头像URL
   */
  getAvatar() {
    return this.userInfo ? this.userInfo.avatar : ''
  }

  /**
   * 获取VIP状态
   * @returns {Object} VIP状态信息
   */
  getVipStatus() {
    return this.userInfo ? this.userInfo.vip : { status: false, expiredAt: null }
  }

  /**
   * 检查是否为VIP会员
   * @returns {boolean} 是否为VIP会员
   */
  isVipMember() {
    const vipStatus = this.getVipStatus()
    return vipStatus.status === true
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isUserLoggedIn() {
    return this.isLoggedIn && this.userInfo !== null
  }

  /**
   * 更新用户信息
   * @param {Object} updateData - 更新的数据
   * @param {string} updateData.nickname - 昵称
   * @param {string} updateData.avatar - 头像
   */
  async updateUserInfo(updateData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'updateUserInfo',
          data: {
            ...updateData
          }
        }
      })

      if (result.result.success) {
        // 更新本地缓存
        if (updateData.nickname !== undefined) {
          this.userInfo.nickname = updateData.nickname
        }
        if (updateData.avatar !== undefined) {
          this.userInfo.avatar = updateData.avatar
        }
        
        this._saveToCache()
        this.notifyChange()

        // 触发用户信息变化的立即同步
        this.notifyProfileChange()

        return { success: true }
      } else {
        throw new Error(result.result.message || '更新用户信息失败')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 强制刷新用户信息
   */
  async refreshUserInfo() {
    try {
      this._clearCache()
      await this._loadFromCloud()
      return { success: true }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 添加变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 通知变化
   */
  notifyChange() {
    this.changeListeners.forEach(listener => {
      try {
        listener(this.userInfo)
      } catch (error) {
        console.error('用户信息变化监听器执行失败:', error)
      }
    })
  }

  /**
   * 通知用户信息变化（用于触发立即同步）
   */
  notifyProfileChange() {
    try {
      // 延迟执行，避免循环依赖
      setTimeout(() => {
        try {
          const app = getApp()
          if (app && app.getSyncManager) {
            const syncManager = app.getSyncManager()
            if (syncManager && syncManager.scheduleSync) {
              syncManager.scheduleSync('user_profile_change')
            }
          }
        } catch (error) {
          console.error('获取同步管理器失败:', error)
        }
      }, 100)
    } catch (error) {
      console.error('通知用户信息变化失败:', error)
    }
  }

  /**
   * 通知用户登录完成（用于触发初始同步检查）
   */
  notifyLoginComplete() {
    try {
      // 延迟执行，确保同步管理器已初始化
      setTimeout(() => {
        try {
          const app = getApp()
          if (app && app.getSyncManager) {
            const syncManager = app.getSyncManager()
            if (syncManager && syncManager.checkInitialSync) {
              console.log('用户登录完成，触发初始同步检查...')
              syncManager.checkInitialSync()
            }
          }
        } catch (error) {
          console.error('触发初始同步检查失败:', error)
        }
      }, 1000) // 1秒延迟，确保同步管理器已初始化
    } catch (error) {
      console.error('通知用户登录完成失败:', error)
    }
  }

  /**
   * 登出
   */
  logout() {
    this._clearCache()
    this.notifyChange()
    console.log('用户已登出')
  }
}

// 导出单例实例
let userManagerInstance = null

function getUserManager() {
  if (!userManagerInstance) {
    userManagerInstance = new UserManager()
  }
  return userManagerInstance
}

module.exports = {
  UserManager,
  getUserManager
}
