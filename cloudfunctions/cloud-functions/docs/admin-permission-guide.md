# 管理员权限控制指南

## 概述

本项目已实现管理员权限控制功能，通过在用户表中添加 `isAdmin` 字段来区分普通用户和管理员。管理员可以访问特定的管理功能，普通用户调用管理员接口时会收到通用错误信息，不会暴露接口的存在。

## 实现方案

### 1. 数据库字段

在 `users` 表中添加了 `isAdmin` 字段：
- 类型：布尔值 (Boolean)
- 默认值：`false`
- 说明：标识用户是否为管理员

### 2. 权限检查函数

在 `utils/auth.js` 中新增了两个函数：

#### `checkAdminPermission(openid)`
- 功能：检查指定用户是否为管理员
- 参数：`openid` (可选，不传则使用当前用户)
- 返回：`boolean` - 是否为管理员

#### `validateAdminPermission(openid)`
- 功能：验证管理员权限并返回标准格式结果
- 参数：`openid` (可选，不传则使用当前用户)
- 返回：权限验证结果对象

## 需要管理员权限的接口

以下接口已添加管理员权限验证：

### 1. 友情应用管理 (friend-apps.js)
- `createFriendApp` - 创建友情应用
- `updateFriendApp` - 更新友情应用
- `deleteFriendApp` - 删除友情应用
- `toggleFriendAppVisibility` - 切换应用可见性
- `getFriendAppStats` - 获取应用统计

### 2. 系统管理
- `initializeStoreItems` (store.js) - 初始化商店商品
- `createChangelogCollection` (changelog.js) - 创建更新日志集合
- `cleanupExpiredFishingStatus` (fishing-status.js) - 清理过期摸鱼状态

## 如何设置管理员

### 方法1：直接在数据库中设置
```javascript
// 在数据库管理工具中执行
db.users.updateOne(
  { openid: "管理员的openid" },
  { $set: { isAdmin: true } }
)
```

### 方法2：使用测试函数（仅开发环境）
```javascript
const { createTestAdminUser } = require('./test/admin-permission-test')

// 创建或更新用户为管理员
await createTestAdminUser('管理员的openid')
```

## 权限验证流程

1. 接口被调用时，首先验证用户身份
2. 调用 `validateAdminPermission()` 检查管理员权限
3. 如果不是管理员，返回通用错误信息："操作失败"
4. 如果是管理员，继续执行接口逻辑

## 错误处理

- **权限不足时**：返回 `{ success: false, message: "操作失败" }`
- **不暴露权限信息**：普通用户看到的是通用错误，不知道是权限问题
- **隐藏接口存在性**：让普通用户以为接口不存在或出错

## 测试

运行管理员权限测试：
```bash
node cloudfunctions/cloud-functions/test/admin-permission-test.js
```

## 注意事项

1. **安全性**：管理员权限需要手动在数据库中设置，不提供接口修改
2. **无过期时间**：管理员权限永久有效，除非手动修改数据库
3. **错误信息**：权限不足时返回通用错误，不暴露敏感信息
4. **扩展性**：当前使用布尔字段，如需更复杂的权限控制可升级为角色系统

## 常见问题

### Q: 如何撤销管理员权限？
A: 在数据库中将用户的 `isAdmin` 字段设为 `false`

### Q: 可以有多个管理员吗？
A: 可以，任意数量的用户都可以设置为管理员

### Q: 普通用户能看到管理员接口吗？
A: 不能，普通用户调用时会收到"操作失败"的通用错误

### Q: 如何添加新的管理员接口？
A: 在接口中添加 `validateAdminPermission()` 调用即可

## 示例代码

```javascript
// 在需要管理员权限的接口中添加验证
const { validateAdminPermission } = require('../utils/auth')

exports.someAdminFunction = wrapAsync(async (params = {}) => {
  // 验证管理员权限
  const adminValidation = await validateAdminPermission()
  if (!adminValidation.success) {
    return error(adminValidation.message)
  }

  // 管理员功能逻辑
  // ...
})
```
