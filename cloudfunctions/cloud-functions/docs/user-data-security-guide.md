# 用户数据安全处理指南

## 安全原则

### 1. 明确字段处理策略
- **不使用排除清理**：不采用 `...data` 然后排除敏感字段的方式
- **明确包含策略**：只处理明确需要的字段
- **逐字段验证**：每个字段都有明确的验证逻辑

### 2. 当前实现

#### getUserInfo 接口
```javascript
exports.getUserInfo = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  
  // 目前不处理任何用户提交的数据
  // 只传递版本信息用于用户创建和版本更新
  const userCreationData = {
    version: version
  }
  
  const result = await ensureUserExists(userCreationData)
  // ...
})
```

#### updateUserInfo 接口
```javascript
exports.updateUserInfo = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  
  // 明确定义允许更新的字段，采用白名单策略
  const updateData = {}
  
  // 处理昵称字段
  if (data.nickname !== undefined) {
    if (typeof data.nickname === 'string' && data.nickname.length <= 50) {
      updateData.nickname = data.nickname.trim()
    } else {
      return error('昵称格式不正确或长度超过限制')
    }
  }
  
  // 处理头像字段
  if (data.avatar !== undefined) {
    if (typeof data.avatar === 'string' && data.avatar.length <= 500) {
      if (data.avatar === '' || /^https?:\/\/.+/.test(data.avatar)) {
        updateData.avatar = data.avatar
      } else {
        return error('头像URL格式不正确')
      }
    } else {
      return error('头像URL长度超过限制')
    }
  }
  
  // 处理设置字段
  if (data.settings !== undefined) {
    if (typeof data.settings === 'object' && data.settings !== null && !Array.isArray(data.settings)) {
      updateData.settings = data.settings
    } else {
      return error('设置数据格式不正确')
    }
  }
  
  // 只更新明确处理的字段
  const result = await usersDB.updateByOpenid(user.openid, updateData)
})
```

## 添加新字段的安全流程

### 步骤1：确定字段需求
在添加新的用户数据字段前，明确：
- 字段名称和用途
- 数据类型和格式要求
- 验证规则和安全限制
- 是否允许用户直接设置

### 步骤2：更新数据库结构
```javascript
// 在 cloudfunctions/cloud-functions/db/users.js 中
const defaultUserData = {
  // 现有字段...
  
  // 新增字段示例
  profile: {
    bio: '',           // 个人简介
    location: '',      // 位置信息
    website: ''        // 个人网站
  }
}
```

### 步骤3：添加字段处理逻辑
```javascript
// 在相应的API函数中添加字段处理
if (data.bio !== undefined) {
  // 验证个人简介
  if (typeof data.bio === 'string' && data.bio.length <= 200) {
    updateData['profile.bio'] = data.bio.trim()
  } else {
    return error('个人简介格式不正确或长度超过限制')
  }
}

if (data.location !== undefined) {
  // 验证位置信息
  if (typeof data.location === 'string' && data.location.length <= 100) {
    updateData['profile.location'] = data.location.trim()
  } else {
    return error('位置信息格式不正确或长度超过限制')
  }
}
```

### 步骤4：添加安全验证
```javascript
// 对于敏感字段，添加额外验证
if (data.website !== undefined) {
  if (typeof data.website === 'string') {
    // 验证URL格式
    if (data.website === '' || /^https?:\/\/[^\s]+$/.test(data.website)) {
      // 检查域名白名单（如果需要）
      const allowedDomains = ['github.com', 'linkedin.com', 'twitter.com']
      if (data.website === '' || allowedDomains.some(domain => data.website.includes(domain))) {
        updateData['profile.website'] = data.website
      } else {
        return error('不支持的网站域名')
      }
    } else {
      return error('网站URL格式不正确')
    }
  } else {
    return error('网站URL必须是字符串')
  }
}
```

## 安全检查清单

### 字段添加前检查
- [ ] 字段是否真的需要用户直接设置？
- [ ] 是否可能被恶意利用？
- [ ] 数据类型和格式是否明确定义？
- [ ] 是否有长度和内容限制？
- [ ] 是否需要特殊的验证逻辑？

### 实现时检查
- [ ] 使用明确的字段名检查，不使用 `...data`
- [ ] 每个字段都有独立的验证逻辑
- [ ] 错误信息明确且不泄露敏感信息
- [ ] 记录必要的安全日志
- [ ] 测试恶意输入的处理

### 部署前检查
- [ ] 测试正常输入的处理
- [ ] 测试异常输入的处理
- [ ] 测试恶意输入的防护
- [ ] 检查日志输出是否合适
- [ ] 确认不会影响现有功能

## 常见安全陷阱

### ❌ 错误做法
```javascript
// 危险：直接使用用户输入
const result = await usersDB.updateByOpenid(user.openid, data)

// 危险：排除式清理
const { openid, _id, isAdmin, ...safeData } = data
const result = await usersDB.updateByOpenid(user.openid, safeData)

// 危险：不验证数据类型
updateData.nickname = data.nickname  // 可能不是字符串
```

### ✅ 正确做法
```javascript
// 安全：明确字段处理
const updateData = {}

if (data.nickname !== undefined) {
  if (typeof data.nickname === 'string' && data.nickname.length <= 50) {
    updateData.nickname = data.nickname.trim()
  } else {
    return error('昵称格式不正确')
  }
}

const result = await usersDB.updateByOpenid(user.openid, updateData)
```

## 总结

通过采用明确的字段处理策略，我们可以：
1. **防止恶意参数注入**：只处理明确定义的字段
2. **提高代码可维护性**：每个字段的处理逻辑清晰明确
3. **便于安全审计**：容易检查和验证安全性
4. **支持灵活扩展**：添加新字段时有明确的安全流程

这种方式虽然代码量稍多，但安全性和可维护性都大大提高。
