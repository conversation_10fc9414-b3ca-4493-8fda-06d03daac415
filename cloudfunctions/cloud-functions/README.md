# 云函数项目架构说明

## 📁 项目结构

```
cloudfunctions/cloud-functions/
├── index.js                # 主入口文件，路由分发
├── package.json            # 依赖配置
├── db/                     # 数据库操作层
│   ├── index.js            # 数据库操作统一入口
│   ├── base.js             # 基础数据库操作类
│   ├── users.js            # 用户数据操作
│   ├── user-data.js        # 用户数据存储操作
│   └── changelog.js        # 更新日志操作
├── utils/                  # 工具函数层
│   ├── index.js            # 工具函数统一入口
│   ├── auth.js             # 认证相关工具
│   ├── response.js         # 响应格式化工具
│   ├── datetime.js         # 日期时间工具
│   └── test-user.js        # 测试用户工具
└── api/                    # API接口层
    ├── user.js             # 用户相关API
    ├── user-data.js        # 数据同步API
    ├── changelog.js        # 更新日志API
    └── holiday.js          # 节假日API
```

## 🏗️ 架构设计原则

### 1. 分层架构
- **API层**: 处理业务逻辑，参数验证，权限检查
- **数据库层**: 封装数据库操作，提供统一的CRUD接口
- **工具层**: 提供通用的工具函数和辅助功能

### 2. 单一职责
- 每个文件只负责一个特定的功能模块
- 数据库操作与业务逻辑分离
- 工具函数独立封装

### 3. 统一规范
- 统一的错误处理机制
- 标准化的响应格式
- 一致的命名规范

## 📊 数据库操作层 (db/)

### BaseDB 基础类
提供通用的数据库操作方法：
- `create(data)` - 创建文档
- `findById(id)` - 根据ID查询
- `find(where, options)` - 条件查询
- `findOne(where)` - 查询单个文档
- `updateById(id, data)` - 根据ID更新
- `update(where, data)` - 条件更新
- `deleteById(id)` - 根据ID删除
- `delete(where)` - 条件删除
- `count(where)` - 统计数量

#### find 方法参数格式
```javascript
// 正确的参数格式
await this.find(where, options)

// options 参数说明：
{
  orderBy: { field: '字段名', order: 'asc'|'desc' },  // 排序
  limit: 数字,                                        // 限制数量
  skip: 数字                                          // 跳过数量
}

// ✅ 正确示例
const result = await this.find({ userId }, {
  orderBy: { field: 'lastModified', order: 'desc' },
  limit: 10
})

// ❌ 错误示例 - 不要使用 sort
const result = await this.find({ userId }, {
  sort: { lastModified: -1 },  // 错误：应该使用 orderBy
  limit: 10
})
```

### 具体实现类
- **UsersDB**: 用户数据操作，继承BaseDB
- **UserDataDB**: 用户数据存储操作
- **ChangelogDB**: 更新日志操作

## 🛠️ 工具函数层 (utils/)

### auth.js - 认证工具
- `getCurrentUser()` - 获取当前用户
- `ensureUserExists()` - 确保用户存在

**注意**: VIP权限相关功能已移除，所有用户均可使用数据同步功能

### response.js - 响应工具
- `success(data, message)` - 成功响应
- `error(message, code)` - 错误响应
- `wrapAsync(fn)` - 异步函数包装器
- `validateRequired(params, fields)` - 参数验证

**注意**: `permissionDenied()` 已移除，不再使用VIP权限检查

### datetime.js - 日期时间工具
- `normalizeTime(date)` - 标准化时间为秒级精度
- `formatDate()` - 格式化日期
- `getToday()` - 获取今天日期
- `getRelativeTime()` - 获取相对时间

## 🔌 API接口层 (api/)

### 统一特点
- 使用 `wrapAsync` 包装异步函数
- 统一的错误处理
- 标准化的响应格式
- 参数验证和权限检查

### user.js - 用户API
- `getUserInfo()` - 获取用户信息
- `updateUserInfo()` - 更新用户信息
- `checkApiPermission()` - 检查API权限

### user-data.js - 同步API
- `getCloudDataInfo()` - 获取云端数据信息
- `downloadUserData()` - 下载用户数据
- `uploadUserData()` - 上传用户数据
- `getHistoryDataList()` - 获取历史数据列表

**数据结构说明**：
- 支持时间段数据、摸鱼记录、收入调整等完整工作数据
- 收入调整包括额外收入和扣款两类，支持自定义类型和描述
- 数据存储采用最小化原则，只保存核心数据，计算字段动态生成

## 🚀 使用示例

### 1. 添加新的数据库操作

```javascript
// db/new-collection.js
const BaseDB = require('./base')

class NewCollectionDB extends BaseDB {
  constructor() {
    super('new_collection')
  }

  async customMethod() {
    // 自定义方法
  }
}

module.exports = new NewCollectionDB()
```

### 2. 添加新的API接口

```javascript
// api/new-api.js
const newCollectionDB = require('../db/new-collection')
const { success, wrapAsync, validateRequired } = require('../utils/response')

exports.newMethod = wrapAsync(async (params = {}) => {
  // 参数验证
  validateRequired(params, ['requiredField'])

  // 从params中解构参数
  const { requiredField, optionalField = 'defaultValue' } = params

  // 业务逻辑处理
  const result = await newCollectionDB.customMethod(requiredField, optionalField)

  if (!result.success) {
    return result
  }

  return success(result.data, '操作成功')
})
```

### 3. 在主入口中注册

```javascript
// index.js
const { newMethod } = require('./api/new-api')

// 在路由中添加 - 统一使用 event.data.data
case "newMethod":
  return await newMethod(event.data.data || {})
```

## 📝 参数传递最佳实践

### 1. 参数解构和默认值
```javascript
// ✅ 推荐：使用解构赋值和默认值
exports.getDataList = wrapAsync(async (params = {}) => {
  const {
    limit = 20,           // 默认值
    skip = 0,             // 默认值
    type,                 // 必需参数
    filters = {}          // 默认空对象
  } = params

  // 参数验证
  validateRequired(params, ['type'])

  // 使用解构后的参数
  return await dataDB.find({ type, ...filters }, { limit, skip })
})

// ❌ 不推荐：直接使用params对象
exports.getDataList = wrapAsync(async (params = {}) => {
  validateRequired(params, ['type'])

  return await dataDB.find(
    { type: params.type, ...params.filters },
    { limit: params.limit || 20, skip: params.skip || 0 }
  )
})
```

### 2. 复杂参数处理
```javascript
// ✅ 推荐：分层解构复杂参数
exports.updateUserSettings = wrapAsync(async (params = {}) => {
  const {
    userId,
    settings: {
      theme = 'light',
      language = 'zh-CN',
      notifications = {}
    } = {},
    metadata = {}
  } = params

  validateRequired(params, ['userId'])

  // 处理嵌套的notifications设置
  const {
    email = true,
    push = true,
    sms = false
  } = notifications

  const updateData = {
    'settings.theme': theme,
    'settings.language': language,
    'settings.notifications.email': email,
    'settings.notifications.push': push,
    'settings.notifications.sms': sms,
    metadata
  }

  return await usersDB.updateById(userId, updateData)
})
```

### 3. 参数验证策略
```javascript
// ✅ 推荐：分层验证
exports.createOrder = wrapAsync(async (params = {}) => {
  // 基础参数验证
  validateRequired(params, ['userId', 'items', 'shippingAddress'])

  const { userId, items, shippingAddress, couponCode } = params

  // 业务逻辑验证
  if (!Array.isArray(items) || items.length === 0) {
    return error('商品列表不能为空')
  }

  // 验证每个商品项
  for (const item of items) {
    validateRequired(item, ['productId', 'quantity'])
    if (item.quantity <= 0) {
      return error('商品数量必须大于0')
    }
  }

  // 验证地址信息
  validateRequired(shippingAddress, ['name', 'phone', 'address'])

  // 处理业务逻辑...
})
```

## ✅ 优势总结

1. **代码复用**: 基础数据库操作类减少重复代码
2. **易于维护**: 清晰的分层结构，职责明确
3. **统一规范**: 标准化的错误处理和响应格式
4. **扩展性强**: 新增功能只需按规范添加对应文件
5. **类型安全**: 完善的参数验证和错误处理
6. **性能优化**: 统一的数据库连接和查询优化
7. **参数传递一致性**: 统一的参数传递规范，避免混乱

## ⏰ 时间处理和数据库查询最佳实践

### 1. 时间标准化
所有时间相关操作必须使用统一的时间标准化工具：

```javascript
const { normalizeTime } = require('../utils/datetime')

// ✅ 正确：使用 normalizeTime 标准化时间
const dataToSave = {
  userId,
  data: userData,
  lastModified: normalizeTime(new Date(userData.lastModified)),
  recordDate: currentDate
}

// ❌ 错误：直接使用原始时间
const dataToSave = {
  userId,
  data: userData,
  lastModified: new Date(userData.lastModified),  // 可能包含毫秒
  recordDate: currentDate
}
```

**重要说明**：
- 使用 `normalizeTime()` 确保时间精度为秒级（毫秒设为0）
- 避免本地毫秒精度与云端秒级精度不一致的问题
- 确保时间比较和同步逻辑的准确性

### 2. 数据库查询参数规范
使用 BaseDB 的 `find` 方法时，必须使用正确的参数格式：

```javascript
// ✅ 正确：使用 orderBy 格式
const result = await this.find({ userId }, {
  orderBy: { field: 'lastModified', order: 'desc' },
  limit: 1
})

// ✅ 正确：历史数据查询
const queryOptions = {
  orderBy: { field: 'lastModified', order: 'desc' },
  limit: options.limit || 20,
  skip: options.skip || 0
}
const result = await this.find({ userId }, queryOptions)

// ❌ 错误：使用 sort 格式（不会生效）
const result = await this.find({ userId }, {
  sort: { lastModified: -1 },  // 错误格式
  limit: 1
})
```

**参数说明**：
- `orderBy.field`: 排序字段名（字符串）
- `orderBy.order`: 排序方向（'asc' 或 'desc'）
- `limit`: 限制返回数量（数字）
- `skip`: 跳过记录数（数字）

### 3. 数据保存策略
实现同一天内只保留最新数据的策略：

```javascript
// ✅ 正确：按日期保存数据
async saveUserData(userId, userData) {
  const userLastModified = userData.lastModified || new Date().toISOString()
  const saveTime = new Date(userLastModified)
  const currentDate = saveTime.toISOString().split('T')[0] // YYYY-MM-DD

  const dataToSave = {
    userId,
    data: userData,
    lastModified: normalizeTime(saveTime),
    recordDate: currentDate  // 用于同一天判断
  }

  // 查找同一天的数据
  const todayDataResult = await this.findOne({ userId, recordDate: currentDate })

  if (todayDataResult.success && todayDataResult.data) {
    // 同一天内有数据，更新（覆盖）
    return await this.updateById(todayDataResult.data._id, dataToSave)
  } else {
    // 同一天内没有数据，创建新记录
    return await this.create(dataToSave)
  }
}
```

## 🔧 开发规范

### 1. 参数传递规范

#### 云函数入口层 (index.js)
所有API调用必须统一使用 `event.data.data` 传递参数：

```javascript
// ✅ 正确的参数传递
case "apiName":
  return await apiFunction(event.data);   // 直接传递event.data

// ❌ 错误的参数传递
case "apiName":
  return await apiFunction(event);        // 传递整个event
case "apiName":
  return await apiFunction();             // 不传递参数
case "apiName":
  return await apiFunction(event.params); // 使用其他字段
```

#### API函数层
所有API函数必须使用统一的参数接收格式：

```javascript
// ✅ 正确的函数签名
exports.apiFunction = wrapAsync(async (params = {}) => {
  // 从 params 中解构需要的参数
  const { param1, param2 = defaultValue } = params;

  // 参数验证
  validateRequired(params, ['param1']);

  // 业务逻辑处理
  const result = await someOperation(param1, param2);

  return success(result);
});

// ❌ 错误的函数签名
exports.apiFunction = wrapAsync(async (param1, param2) => { ... });  // 直接传递多个参数
exports.apiFunction = wrapAsync(async (event) => { ... });           // 传递event对象
exports.apiFunction = wrapAsync(async () => { ... });               // 无参数接收
```

#### 小程序调用层
小程序端调用云函数时必须使用标准格式：

```javascript
// ✅ 正确的调用方式
const result = await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'apiName',
    data: {
      param1: value1,
      param2: value2
    }
  }
});

// ❌ 错误的调用方式
const result = await wx.cloud.callFunction({
  name: 'cloud-functions',
  data: {
    type: 'apiName',
    param1: value1,        // 直接在data下放置参数
    param2: value2
  }
});
```

### 2. 其他开发规范

1. **命名规范**: 使用驼峰命名法，文件名使用短横线分隔
2. **错误处理**: 所有异步操作都要有错误处理
3. **参数验证**: API接口必须验证必需参数
4. **权限检查**: 涉及用户数据的操作要检查权限
5. **响应格式**: 使用统一的响应格式化工具
6. **文档注释**: 每个函数都要有清晰的注释说明

## ⚠️ 常见错误和解决方案

### 1. 时间处理错误

**错误**: 时间精度不一致导致同步失败
```javascript
// ❌ 错误原因：直接使用包含毫秒的时间
const dataToSave = {
  lastModified: new Date(userData.lastModified)  // 可能是 2025-01-26T10:30:45.123Z
}

// ✅ 解决方案：使用 normalizeTime 标准化
const { normalizeTime } = require('../utils/datetime')
const dataToSave = {
  lastModified: normalizeTime(new Date(userData.lastModified))  // 变为 2025-01-26T10:30:45.000Z
}
```

**错误**: 重复的时间标准化代码
```javascript
// ❌ 错误做法：在每个文件中重复定义
normalizeTime(date) {
  const dateObj = new Date(date)
  dateObj.setMilliseconds(0)
  return dateObj.toISOString()
}

// ✅ 正确做法：使用统一的工具函数
const { normalizeTime } = require('../utils/datetime')
```

### 2. 数据库查询错误

**错误**: 使用错误的排序参数格式
```javascript
// ❌ 错误原因：使用了 sort 而不是 orderBy
const result = await this.find({ userId }, {
  sort: { lastModified: -1 },  // 不会生效
  limit: 1
})

// ✅ 解决方案：使用正确的 orderBy 格式
const result = await this.find({ userId }, {
  orderBy: { field: 'lastModified', order: 'desc' },
  limit: 1
})
```

**错误**: 数据获取排序失效
```javascript
// ❌ 错误现象：无法获取最新数据
// 原因：排序参数格式错误，数据库返回的不是按时间排序的结果

// ✅ 解决方案：确保使用正确的参数格式
const queryOptions = {
  orderBy: { field: 'lastModified', order: 'desc' },
  limit: options.limit || 20,
  skip: options.skip || 0
}
```

### 3. 参数传递错误

**错误**: `Cannot read properties of undefined`
```javascript
// ❌ 错误原因：没有使用event.data.data
case "getUserInfo":
  return await getUserInfo(event.data);  // 应该使用event.data.data

// ✅ 解决方案：统一使用event.data.data
case "getUserInfo":
  return await getUserInfo(event.data.data || {});
```

**错误**: 函数接收到undefined参数
```javascript
// ❌ 错误原因：函数没有默认参数
exports.getList = wrapAsync(async (params) => {
  const { limit } = params;  // params可能是undefined
});

// ✅ 解决方案：添加默认参数
exports.getList = wrapAsync(async (params = {}) => {
  const { limit = 20 } = params;
});
```

### 2. 参数验证错误

**错误**: 参数验证失败但没有明确提示
```javascript
// ❌ 错误做法：验证失败后继续执行
exports.updateUser = wrapAsync(async (params = {}) => {
  if (!params.userId) {
    console.log('userId is required');  // 只打印日志
  }
  // 继续执行，可能导致错误
});

// ✅ 正确做法：使用validateRequired
exports.updateUser = wrapAsync(async (params = {}) => {
  validateRequired(params, ['userId']);  // 自动返回错误响应
  // 验证通过后才会执行到这里
});
```

### 3. 数据库操作错误

**错误**: 直接使用数据库实例而不是继承BaseDB
```javascript
// ❌ 错误做法：直接操作数据库
const db = cloud.database();
const result = await db.collection('users').add({...});

// ✅ 正确做法：使用BaseDB封装
const usersDB = require('../db/users');
const result = await usersDB.create({...});
```

### 4. 响应格式错误

**错误**: 不使用统一的响应格式
```javascript
// ❌ 错误做法：直接返回数据
exports.getData = wrapAsync(async (params = {}) => {
  const data = await someOperation();
  return data;  // 格式不统一
});

// ✅ 正确做法：使用success/error包装
exports.getData = wrapAsync(async (params = {}) => {
  const data = await someOperation();
  return success(data, '获取成功');
});
```
