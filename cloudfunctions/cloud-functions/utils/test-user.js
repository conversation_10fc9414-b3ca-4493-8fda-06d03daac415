/**
 * 测试用户相关工具函数
 */

/**
 * 测试用户openid列表
 * 在这里配置测试用户的openid
 */
const TEST_USER_OPENIDS = [
  // 在这里添加测试用户的openid
  // 'test_openid_1',
  // 'test_openid_2'
]

/**
 * 检查用户是否为测试用户
 * @param {string} openid - 用户openid
 * @returns {boolean} 是否为测试用户
 */
function isTestUser(openid) {
  if (!openid) {
    return false
  }
  
  return TEST_USER_OPENIDS.includes(openid)
}

/**
 * 获取测试用户列表
 * @returns {Array<string>} 测试用户openid列表
 */
function getTestUserList() {
  return [...TEST_USER_OPENIDS]
}

/**
 * 添加测试用户
 * @param {string} openid - 用户openid
 * @returns {boolean} 是否添加成功
 */
function addTestUser(openid) {
  if (!openid || TEST_USER_OPENIDS.includes(openid)) {
    return false
  }
  
  TEST_USER_OPENIDS.push(openid)
  return true
}

/**
 * 移除测试用户
 * @param {string} openid - 用户openid
 * @returns {boolean} 是否移除成功
 */
function removeTestUser(openid) {
  const index = TEST_USER_OPENIDS.indexOf(openid)
  if (index === -1) {
    return false
  }
  
  TEST_USER_OPENIDS.splice(index, 1)
  return true
}

module.exports = {
  isTestUser,
  getTestUserList,
  addTestUser,
  removeTestUser
}
